#!/usr/bin/env python3
"""
DINOv2快速修复脚本
专门解决 /home/<USER>/dinov2/JinWooChoi 目录下的导入问题
"""

import os
import sys
import shutil

def main():
    print("=" * 60)
    print("DINOv2 Quick Fix for /home/<USER>/dinov2/JinWooChoi")
    print("=" * 60)
    
    # 当前脚本位置
    current_dir = "/home/<USER>/dinov2/JinWooChoi"  # 您的脚本位置
    dinov2_source = "/home/<USER>/dinov2"  # dinov2模块的实际位置
    
    print(f"Script directory: {current_dir}")
    print(f"DINOv2 source: {dinov2_source}")
    
    # 检查源目录是否存在
    if not os.path.exists(dinov2_source):
        print(f"❌ DINOv2 source directory not found: {dinov2_source}")
        print("Please check if the dinov2 directory exists at /home/<USER>/dinov2")
        return False
    
    # 检查关键文件
    vit_file = os.path.join(dinov2_source, "vision_transformer.py")
    if not os.path.exists(vit_file):
        print(f"❌ vision_transformer.py not found at: {vit_file}")
        return False
    
    print("✓ Found DINOv2 source files")
    
    # 解决方案1: 创建符号链接
    target_link = os.path.join(current_dir, "dinov2")
    
    try:
        if os.path.exists(target_link):
            if os.path.islink(target_link):
                os.unlink(target_link)
                print("Removed existing symbolic link")
            elif os.path.isdir(target_link):
                print("Directory 'dinov2' already exists, skipping symbolic link")
            else:
                os.remove(target_link)
                print("Removed existing file")
        
        # 创建符号链接
        os.symlink(dinov2_source, target_link)
        print(f"✓ Created symbolic link: {target_link} -> {dinov2_source}")
        
    except OSError as e:
        print(f"❌ Failed to create symbolic link: {e}")
        print("Trying alternative solution...")
        
        # 解决方案2: 复制文件
        try:
            if os.path.exists(target_link):
                shutil.rmtree(target_link)
            shutil.copytree(dinov2_source, target_link)
            print(f"✓ Copied DINOv2 directory to: {target_link}")
        except Exception as e2:
            print(f"❌ Failed to copy directory: {e2}")
            return False
    
    # 验证修复
    print("\nVerifying fix...")
    
    # 检查文件是否可访问
    test_files = [
        os.path.join(target_link, "vision_transformer.py"),
        os.path.join(target_link, "__init__.py")
    ]
    
    for test_file in test_files:
        if os.path.exists(test_file):
            print(f"✓ {os.path.basename(test_file)} accessible")
        else:
            print(f"✗ {os.path.basename(test_file)} not found")
    
    # 测试导入
    print("\nTesting import...")
    sys.path.insert(0, current_dir)
    
    try:
        import dinov2
        print("✓ dinov2 import successful")
        
        try:
            from dinov2 import vision_transformer as vits
            print("✓ vision_transformer import successful")
            
            # 测试模型创建
            try:
                model = vits.vit_base(patch_size=14)
                print(f"✓ Model creation successful (embed_dim: {model.embed_dim})")
                return True
            except Exception as e:
                print(f"⚠ Model creation failed: {e}")
                print("But import is working, you can proceed with training")
                return True
                
        except ImportError as e:
            print(f"✗ vision_transformer import failed: {e}")
            return False
            
    except ImportError as e:
        print(f"✗ dinov2 import failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 Fix completed successfully!")
        print("\nYou can now run:")
        print("  python dinov2_all.py")
        print("  python dinov2_simple.py")
    else:
        print("❌ Fix failed. Please try manual solutions:")
        print("\n1. Add to your script:")
        print("   sys.path.insert(0, '/home/<USER>/dinov2')")
        print("\n2. Or set environment variable:")
        print("   export PYTHONPATH=/home/<USER>/dinov2:$PYTHONPATH")
        print("\n3. Or run from parent directory:")
        print("   cd /home/<USER>/dinov2 && python JinWooChoi/dinov2_all.py")
    
    print("=" * 60)

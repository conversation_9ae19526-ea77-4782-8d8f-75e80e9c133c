#!/usr/bin/env python3
"""
DINOv2设置测试脚本
用于验证环境和模型是否能正确加载
"""

import os
import sys
import torch
import torch.nn as nn
from PIL import Image
import torchvision.transforms as transforms
import numpy as np

# 添加dinov2模块路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试模块导入"""
    print("Testing imports...")
    try:
        import dinov2
        from dinov2 import vision_transformer as vits
        print("✓ DINOv2 modules imported successfully")
        return True
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False

def test_model_creation():
    """测试模型创建"""
    print("\nTesting model creation...")
    try:
        from dinov2 import vision_transformer as vits
        
        # 测试不同大小的模型
        models_to_test = [
            ('vit_small', vits.vit_small),
            ('vit_base', vits.vit_base),
            ('vit_large', vits.vit_large),
        ]
        
        for name, model_func in models_to_test:
            try:
                model = model_func(patch_size=14)
                print(f"✓ {name} created successfully (embed_dim: {model.embed_dim})")
            except Exception as e:
                print(f"✗ {name} creation failed: {e}")
                return False
        
        return True
    except Exception as e:
        print(f"✗ Model creation error: {e}")
        return False

def test_classifier():
    """测试分类器"""
    print("\nTesting DINOv2 classifier...")
    try:
        # 导入分类器类
        sys.path.append('.')
        from dinov2_config import DEVICE
        
        # 简化的分类器类
        class DINOv2Classifier(nn.Module):
            def __init__(self, backbone_name='vit_base', num_classes=10):
                super().__init__()
                from dinov2 import vision_transformer as vits
                
                if backbone_name == 'vit_small':
                    self.backbone = vits.vit_small(patch_size=14)
                elif backbone_name == 'vit_base':
                    self.backbone = vits.vit_base(patch_size=14)
                elif backbone_name == 'vit_large':
                    self.backbone = vits.vit_large(patch_size=14)
                else:
                    raise ValueError(f"Unsupported backbone: {backbone_name}")
                
                embed_dim = self.backbone.embed_dim
                self.classifier = nn.Sequential(
                    nn.Dropout(0.5),
                    nn.Linear(embed_dim, 512),
                    nn.ReLU(),
                    nn.Linear(512, num_classes)
                )
            
            def forward(self, x):
                with torch.no_grad():
                    features = self.backbone.forward_features(x)
                    cls_features = features['x_norm_clstoken']
                return self.classifier(cls_features)
        
        # 创建分类器
        model = DINOv2Classifier('vit_base', num_classes=5)
        print(f"✓ DINOv2 classifier created successfully")
        
        # 测试前向传播
        dummy_input = torch.randn(2, 3, 224, 224)
        with torch.no_grad():
            output = model(dummy_input)
        print(f"✓ Forward pass successful, output shape: {output.shape}")
        
        return True
    except Exception as e:
        print(f"✗ Classifier test failed: {e}")
        return False

def test_data_transforms():
    """测试数据预处理"""
    print("\nTesting data transforms...")
    try:
        # 创建测试图像
        test_image = Image.new('RGB', (256, 256), color='red')
        
        # 定义变换
        transform = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
        ])
        
        # 应用变换
        transformed = transform(test_image)
        print(f"✓ Transform successful, shape: {transformed.shape}")
        
        return True
    except Exception as e:
        print(f"✗ Transform test failed: {e}")
        return False

def test_device():
    """测试设备配置"""
    print("\nTesting device configuration...")
    try:
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"✓ Device: {device}")
        
        if torch.cuda.is_available():
            print(f"✓ CUDA available, GPU count: {torch.cuda.device_count()}")
            print(f"✓ Current GPU: {torch.cuda.get_device_name()}")
        else:
            print("! CUDA not available, will use CPU")
        
        return True
    except Exception as e:
        print(f"✗ Device test failed: {e}")
        return False

def test_config_file():
    """测试配置文件"""
    print("\nTesting configuration file...")
    try:
        from dinov2_config import *
        print(f"✓ Config loaded successfully")
        print(f"  - Backbone: {BACKBONE_NAME}")
        print(f"  - Batch size: {BATCH_SIZE}")
        print(f"  - Learning rate: {LEARNING_RATE}")
        print(f"  - Device: {DEVICE}")
        return True
    except Exception as e:
        print(f"✗ Config test failed: {e}")
        return False

def main():
    """运行所有测试"""
    print("=" * 60)
    print("DINOv2 Setup Test")
    print("=" * 60)
    
    tests = [
        test_imports,
        test_device,
        test_config_file,
        test_model_creation,
        test_data_transforms,
        test_classifier,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ Test failed with exception: {e}")
    
    print("\n" + "=" * 60)
    print(f"Test Results: {passed}/{total} passed")
    
    if passed == total:
        print("🎉 All tests passed! DINOv2 setup is ready.")
        print("\nYou can now run:")
        print("  python dinov2_train.py  # 使用配置文件版本")
        print("  python dinov2_all.py    # 使用完整版本")
    else:
        print("❌ Some tests failed. Please check the errors above.")
    
    print("=" * 60)

if __name__ == "__main__":
    main()

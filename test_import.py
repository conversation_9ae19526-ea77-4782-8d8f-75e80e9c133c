#!/usr/bin/env python3
"""
测试DINOv2导入的简单脚本
"""

import os
import sys
import importlib.util

def test_import():
    print("=" * 60)
    print("Testing DINOv2 Import")
    print("=" * 60)
    
    # 文件路径
    DINOV2_PATH = '/home/<USER>/dinov2'
    VIT_FILE = '/home/<USER>/dinov2/vision_transformer.py'
    
    print(f"DINOv2 directory: {DINOV2_PATH}")
    print(f"Vision transformer file: {VIT_FILE}")
    
    # 检查目录和文件
    if not os.path.exists(DINOV2_PATH):
        print(f"❌ Directory not found: {DINOV2_PATH}")
        return False
    
    if not os.path.exists(VIT_FILE):
        print(f"❌ File not found: {VIT_FILE}")
        return False
    
    print("✓ Files exist")
    
    # 检查文件内容
    try:
        with open(VIT_FILE, 'r') as f:
            content = f.read()
        print(f"✓ File readable, size: {len(content)} characters")
        
        # 检查是否包含关键函数
        if 'vit_base' in content:
            print("✓ Found vit_base function")
        if 'vit_large' in content:
            print("✓ Found vit_large function")
            
    except Exception as e:
        print(f"❌ Error reading file: {e}")
        return False
    
    # 尝试导入
    print("\nTesting import methods...")
    
    # 方法1: importlib
    try:
        sys.path.insert(0, DINOV2_PATH)
        
        spec = importlib.util.spec_from_file_location("vision_transformer", VIT_FILE)
        vits = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(vits)
        
        print("✓ Method 1 (importlib) successful")
        
        # 测试创建模型
        try:
            model = vits.vit_base(patch_size=14)
            print(f"✓ Model creation successful (embed_dim: {model.embed_dim})")
            return True
        except Exception as e:
            print(f"⚠ Model creation failed: {e}")
            print("But import is working")
            return True
            
    except Exception as e:
        print(f"❌ Method 1 failed: {e}")
    
    # 方法2: 直接添加路径并导入
    try:
        if DINOV2_PATH not in sys.path:
            sys.path.insert(0, DINOV2_PATH)
        
        # 清除可能的缓存
        if 'vision_transformer' in sys.modules:
            del sys.modules['vision_transformer']
        
        import vision_transformer as vits
        print("✓ Method 2 (direct import) successful")
        
        # 测试创建模型
        try:
            model = vits.vit_base(patch_size=14)
            print(f"✓ Model creation successful (embed_dim: {model.embed_dim})")
            return True
        except Exception as e:
            print(f"⚠ Model creation failed: {e}")
            print("But import is working")
            return True
            
    except Exception as e:
        print(f"❌ Method 2 failed: {e}")
    
    return False

def main():
    success = test_import()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 Import test successful!")
        print("You can now run the training script")
    else:
        print("❌ Import test failed")
        print("Please check the vision_transformer.py file for errors")
    print("=" * 60)

if __name__ == "__main__":
    main()

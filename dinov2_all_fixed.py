#!/usr/bin/env python3
"""
DINOv2训练脚本 - 修复版本
专门针对 /home/<USER>/dinov2/JinWooChoi 目录结构
"""

import torch.optim as optim
import csv
import os
from PIL import Image
import torch
import torch.nn as nn
import torchvision.transforms as transforms
from torch.utils.data import DataLoader
from sklearn.metrics import classification_report, accuracy_score, roc_auc_score, confusion_matrix
from sklearn.metrics import precision_score, recall_score, f1_score
from sklearn.preprocessing import label_binarize
import numpy as np
import sys

# 直接导入vision_transformer.py文件 - 绕过模块导入问题
DINOV2_PATH = '/home/<USER>/dinov2'
VIT_FILE = '/home/<USER>/dinov2/vision_transformer.py'

print(f"DINOv2 path: {DINOV2_PATH}")
print(f"Vision transformer file: {VIT_FILE}")

# 检查文件是否存在
if not os.path.exists(VIT_FILE):
    print(f"❌ vision_transformer.py not found at: {VIT_FILE}")
    sys.exit(1)

print("✓ vision_transformer.py found")

# 方法1: 使用importlib直接导入文件
import importlib.util
try:
    spec = importlib.util.spec_from_file_location("vision_transformer", VIT_FILE)
    vits = importlib.util.module_from_spec(spec)

    # 添加必要的路径到sys.path，以便vision_transformer.py能找到其依赖
    sys.path.insert(0, DINOV2_PATH)

    # 执行模块
    spec.loader.exec_module(vits)
    print("✓ vision_transformer imported successfully using importlib")

    # 创建dinov2命名空间
    class DinOv2Module:
        vision_transformer = vits
    dinov2 = DinOv2Module()

except Exception as e:
    print(f"❌ importlib method failed: {e}")

    # 方法2: 使用exec直接执行文件内容
    try:
        print("Trying exec method...")

        # 读取vision_transformer.py文件内容
        with open(VIT_FILE, 'r', encoding='utf-8') as f:
            vit_code = f.read()

        # 创建执行环境
        vit_globals = {
            '__name__': 'vision_transformer',
            '__file__': VIT_FILE,
        }

        # 添加必要的导入到执行环境
        exec("import sys; sys.path.insert(0, '/home/<USER>/dinov2')", vit_globals)

        # 执行vision_transformer代码
        exec(vit_code, vit_globals)

        # 创建vits模块对象
        class VitsModule:
            pass

        vits = VitsModule()

        # 复制所有函数和类到vits对象
        for name, obj in vit_globals.items():
            if not name.startswith('__') and callable(obj):
                setattr(vits, name, obj)

        print("✓ vision_transformer imported successfully using exec")

        # 创建dinov2命名空间
        class DinOv2Module:
            vision_transformer = vits
        dinov2 = DinOv2Module()

    except Exception as e2:
        print(f"❌ exec method also failed: {e2}")
        print("Please check if vision_transformer.py has syntax errors or missing dependencies")
        sys.exit(1)

# 判断是否可用GPU
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"Using device: {device}")

# 定义超参数
learning_rate = 0.001
batch_size = 32
num_epochs = 50
csv_file_path = r'/home/<USER>/Cytofm/JinWooChoi/train_performance.csv'
model_save_path = r'/home/<USER>/Cytofm/JinWooChoi/trained_dinov2_model.pth'

# 创建保存目录
os.makedirs(os.path.dirname(csv_file_path), exist_ok=True)
os.makedirs(os.path.dirname(model_save_path), exist_ok=True)

# Custom dataset class
class CervixDataset(torch.utils.data.Dataset):
    def __init__(self, txt_file, class_mapping_file, transform=None):
        self.data = []
        self.labels = []
        self.transform = transform
        self.classes = []

        # Load class mapping
        class_mapping = {}
        with open(class_mapping_file, 'r') as f:
            for line in f:
                parts = line.strip().split()
                if len(parts) >= 2:
                    class_name = ' '.join(parts[:-1])
                    idx = int(parts[-1])
                    class_mapping[class_name] = idx
                    while len(self.classes) <= idx:
                        self.classes.append("")
                    self.classes[idx] = class_name

        # Read TXT file
        with open(txt_file, 'r') as f:
            for line in f:
                parts = line.strip().split()
                if len(parts) > 1:
                    img_path = parts[0]
                    class_label = ' '.join(parts[1:])
                    class_idx = class_mapping[class_label]
                    self.data.append(img_path)
                    self.labels.append(class_idx)

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):
        img_path = self.data[idx]
        label = self.labels[idx]

        if not os.path.exists(img_path):
            raise FileNotFoundError(f"Image not found: {img_path}")

        image = Image.open(img_path).convert('RGB')
        if self.transform:
            image = self.transform(image)
        return image, label

# DINOv2分类器类
class DINOv2Classifier(nn.Module):
    def __init__(self, backbone_name='vit_large', num_classes=10, freeze_backbone=True, dropout_rate=0.5):
        super().__init__()
        
        # 创建DINOv2 backbone
        print(f"Creating {backbone_name} backbone...")
        try:
            if backbone_name == 'vit_small':
                self.backbone = vits.vit_small(patch_size=14)
            elif backbone_name == 'vit_base':
                self.backbone = vits.vit_base(patch_size=14)
            elif backbone_name == 'vit_large':
                self.backbone = vits.vit_large(patch_size=14)
            elif backbone_name == 'vit_giant2':
                self.backbone = vits.vit_giant2(patch_size=14)
            else:
                raise ValueError(f"Unsupported backbone: {backbone_name}")
        except Exception as e:
            print(f"Error creating {backbone_name}: {e}")
            print("Trying vit_base as fallback...")
            self.backbone = vits.vit_base(patch_size=14)
            backbone_name = 'vit_base'
        
        # 冻结backbone参数
        if freeze_backbone:
            for param in self.backbone.parameters():
                param.requires_grad = False
            print("DINOv2 backbone frozen")
        else:
            print("DINOv2 backbone will be fine-tuned")
        
        # 获取嵌入维度
        embed_dim = self.backbone.embed_dim
        
        # 分类头
        self.classifier = nn.Sequential(
            nn.Dropout(p=dropout_rate),
            nn.Linear(embed_dim, 512),
            nn.ReLU(),
            nn.Dropout(p=dropout_rate/2),
            nn.Linear(512, num_classes)
        )
        
        print(f"DINOv2 Classifier created:")
        print(f"  - Backbone: {backbone_name}")
        print(f"  - Embed dim: {embed_dim}")
        print(f"  - Num classes: {num_classes}")
        print(f"  - Dropout rate: {dropout_rate}")
    
    def forward(self, x):
        # 提取特征
        if not any(p.requires_grad for p in self.backbone.parameters()):
            with torch.no_grad():
                features = self.backbone.forward_features(x)
                cls_features = features['x_norm_clstoken']
        else:
            features = self.backbone.forward_features(x)
            cls_features = features['x_norm_clstoken']
        
        return self.classifier(cls_features)

def main():
    """主训练函数"""
    print(f"\n{'='*60}")
    print(f"Starting DINOv2 training...")
    print(f"{'='*60}")
    
    # 数据预处理
    train_transform = transforms.Compose([
        transforms.Resize((224, 224)),
        transforms.RandomHorizontalFlip(),
        transforms.RandomRotation(10),
        transforms.ColorJitter(brightness=0.2, contrast=0.2, saturation=0.2, hue=0.1),
        transforms.ToTensor(),
        transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
    ])

    val_transform = transforms.Compose([
        transforms.Resize((224, 224)),
        transforms.ToTensor(),
        transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
    ])

    # 创建数据集
    print("Loading datasets...")
    train_dataset = CervixDataset(
        '/home/<USER>/JinWooChoi/train.txt',
        '/home/<USER>/JinWooChoi/class_mapping.txt',
        transform=train_transform
    )

    val_dataset = CervixDataset(
        '/home/<USER>/JinWooChoi/val.txt',
        '/home/<USER>/JinWooChoi/class_mapping.txt',
        transform=val_transform
    )

    test_dataset = CervixDataset(
        '/home/<USER>/JinWooChoi/test.txt',
        '/home/<USER>/JinWooChoi/class_mapping.txt',
        transform=val_transform
    )

    # 数据加载器
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=4)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=4)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False, num_workers=4)

    num_classes = len(set(train_dataset.labels))
    print(f"Number of classes: {num_classes}")
    print(f"Class names: {train_dataset.classes}")

    # 创建DINOv2分类器模型
    model = DINOv2Classifier(
        backbone_name='vit_large',
        num_classes=num_classes,
        freeze_backbone=True,
        dropout_rate=0.5
    )

    # 将模型移动到设备
    model.to(device)

    # 打印模型参数信息
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"Total parameters: {total_params:,}")
    print(f"Trainable parameters: {trainable_params:,}")

    # 选择损失函数和优化器
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.Adam(
        filter(lambda p: p.requires_grad, model.parameters()), 
        lr=learning_rate, 
        weight_decay=1e-4
    )
    scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=num_epochs)

    # 记录损失和准确率
    train_losses = []
    val_losses = []
    train_accuracies = []
    val_accuracies = []

    # CSV 文件写入
    with open(csv_file_path, mode='w', newline='') as file:
        writer = csv.writer(file)
        writer.writerow(['Epoch', 'Train Loss', 'Train Accuracy', 'Val Loss', 'Val Accuracy'])

    print(f"\nStarting training for {num_epochs} epochs...")

    # 训练模型
    for epoch in range(num_epochs):
        model.train()
        running_loss = 0.0
        correct = 0
        total = 0

        for batch_idx, (images, labels) in enumerate(train_loader):
            images, labels = images.to(device), labels.to(device)
            optimizer.zero_grad()
            outputs = model(images)
            loss = criterion(outputs, labels)
            loss.backward()
            optimizer.step()

            running_loss += loss.item()
            _, predicted = torch.max(outputs.data, 1)
            total += labels.size(0)
            correct += (predicted == labels).sum().item()

            if batch_idx % 10 == 0:
                print(f'Epoch [{epoch+1}/{num_epochs}], Batch [{batch_idx}/{len(train_loader)}], '
                      f'Loss: {loss.item():.4f}')

        train_loss = running_loss / len(train_loader)
        train_accuracy = 100 * correct / total
        train_losses.append(train_loss)
        train_accuracies.append(train_accuracy)

        # 验证模型
        model.eval()
        running_loss = 0.0
        correct = 0
        total = 0

        with torch.no_grad():
            for images, labels in val_loader:
                images, labels = images.to(device), labels.to(device)
                outputs = model(images)
                loss = criterion(outputs, labels)
                running_loss += loss.item()
                _, predicted = torch.max(outputs.data, 1)
                total += labels.size(0)
                correct += (predicted == labels).sum().item()

        val_loss = running_loss / len(val_loader)
        val_accuracy = 100 * correct / total
        val_losses.append(val_loss)
        val_accuracies.append(val_accuracy)

        print(f'Epoch [{epoch + 1}/{num_epochs}], '
              f'Train Loss: {train_loss:.4f}, Train Accuracy: {train_accuracy:.2f}%, '
              f'Val Loss: {val_loss:.4f}, Val Accuracy: {val_accuracy:.2f}%')

        # 保存训练过程数据到 CSV
        with open(csv_file_path, mode='a', newline='') as file:
            writer = csv.writer(file)
            writer.writerow([epoch + 1, train_loss, train_accuracy, val_loss, val_accuracy])

        scheduler.step()

    # 保存训练好的模型
    torch.save(model.state_dict(), model_save_path)
    print(f"\nModel saved to: {model_save_path}")

    print(f"\n{'='*60}")
    print(f"Starting DINOv2 evaluation...")
    print(f"{'='*60}")

    # 测试评估
    model.eval()
    all_preds = []
    all_labels = []
    all_probs = []

    with torch.no_grad():
        for images, labels in test_loader:
            images, labels = images.to(device), labels.to(device)
            outputs = model(images)
            _, preds = torch.max(outputs, 1)
            probs = torch.nn.functional.softmax(outputs, dim=1)

            all_preds.extend(preds.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())
            all_probs.append(probs.cpu().numpy())

    all_probs = np.vstack(all_probs)
    all_preds = np.array(all_preds)
    all_labels = np.array(all_labels)

    # 计算评估指标
    accuracy = accuracy_score(all_labels, all_preds)
    macro_precision = precision_score(all_labels, all_preds, average='macro')
    macro_recall = recall_score(all_labels, all_preds, average='macro')
    macro_f1 = f1_score(all_labels, all_preds, average='macro')

    print(f"\nDINOv2 Evaluation Results:")
    print(f"Accuracy: {accuracy:.4f}")
    print(f"Macro Precision: {macro_precision:.4f}")
    print(f"Macro Recall: {macro_recall:.4f}")
    print(f"Macro F1: {macro_f1:.4f}")

    # 分类报告
    print("\nClassification Report:")
    print(classification_report(all_labels, all_preds, target_names=test_dataset.classes))

    # 保存结果
    results_file = model_save_path.replace('.pth', '_results.txt')
    with open(results_file, 'w') as f:
        f.write("DINOv2 Model Evaluation Results\n")
        f.write("="*50 + "\n")
        f.write(f"Accuracy: {accuracy:.4f}\n")
        f.write(f"Macro Precision: {macro_precision:.4f}\n")
        f.write(f"Macro Recall: {macro_recall:.4f}\n")
        f.write(f"Macro F1: {macro_f1:.4f}\n")
        f.write("\nClassification Report:\n")
        f.write(classification_report(all_labels, all_preds, target_names=test_dataset.classes))

    print(f"\nResults saved to: {results_file}")
    print(f"Training log saved to: {csv_file_path}")
    print(f"\n{'='*60}")
    print(f"DINOv2 training and evaluation completed!")
    print(f"{'='*60}")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
DINOv2最简化训练脚本
完全绕过模块导入问题
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torchvision.transforms as transforms
from torch.utils.data import DataLoader
from PIL import Image
import os
import sys
import importlib.util
import csv
from sklearn.metrics import classification_report, accuracy_score
import numpy as np

# 设备配置
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"Using device: {device}")

# 直接导入vision_transformer
def import_vision_transformer():
    """直接导入vision_transformer模块"""
    VIT_FILE = '/home/<USER>/dinov2/vision_transformer.py'
    DINOV2_PATH = '/home/<USER>/dinov2'
    
    if not os.path.exists(VIT_FILE):
        raise FileNotFoundError(f"vision_transformer.py not found: {VIT_FILE}")
    
    # 添加路径
    sys.path.insert(0, DINOV2_PATH)
    
    # 使用importlib导入
    spec = importlib.util.spec_from_file_location("vision_transformer", VIT_FILE)
    vits = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(vits)
    
    return vits

# 导入vision_transformer
print("Importing vision_transformer...")
try:
    vits = import_vision_transformer()
    print("✓ vision_transformer imported successfully")
except Exception as e:
    print(f"❌ Failed to import vision_transformer: {e}")
    sys.exit(1)

# 数据集类
class CervixDataset(torch.utils.data.Dataset):
    def __init__(self, txt_file, class_mapping_file, transform=None):
        self.data = []
        self.labels = []
        self.transform = transform
        self.classes = []

        # 加载类别映射
        class_mapping = {}
        with open(class_mapping_file, 'r') as f:
            for line in f:
                parts = line.strip().split()
                if len(parts) >= 2:
                    class_name = ' '.join(parts[:-1])
                    idx = int(parts[-1])
                    class_mapping[class_name] = idx
                    while len(self.classes) <= idx:
                        self.classes.append("")
                    self.classes[idx] = class_name

        # 读取数据文件
        with open(txt_file, 'r') as f:
            for line in f:
                parts = line.strip().split()
                if len(parts) > 1:
                    img_path = parts[0]
                    class_label = ' '.join(parts[1:])
                    class_idx = class_mapping[class_label]
                    self.data.append(img_path)
                    self.labels.append(class_idx)

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):
        img_path = self.data[idx]
        label = self.labels[idx]

        if not os.path.exists(img_path):
            raise FileNotFoundError(f"Image not found: {img_path}")

        image = Image.open(img_path).convert('RGB')
        if self.transform:
            image = self.transform(image)
        return image, label

# 简化的DINOv2分类器
class SimpleDINOv2Classifier(nn.Module):
    def __init__(self, num_classes=10, backbone_name='vit_base'):
        super().__init__()
        
        print(f"Creating {backbone_name} backbone...")
        
        # 创建backbone
        if backbone_name == 'vit_small':
            self.backbone = vits.vit_small(patch_size=14)
        elif backbone_name == 'vit_base':
            self.backbone = vits.vit_base(patch_size=14)
        elif backbone_name == 'vit_large':
            self.backbone = vits.vit_large(patch_size=14)
        else:
            print(f"Unknown backbone {backbone_name}, using vit_base")
            self.backbone = vits.vit_base(patch_size=14)
        
        # 冻结backbone
        for param in self.backbone.parameters():
            param.requires_grad = False
        
        # 分类头
        embed_dim = self.backbone.embed_dim
        self.classifier = nn.Sequential(
            nn.Dropout(0.5),
            nn.Linear(embed_dim, 512),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(512, num_classes)
        )
        
        print(f"✓ Model created with embed_dim: {embed_dim}")
    
    def forward(self, x):
        with torch.no_grad():
            features = self.backbone.forward_features(x)
            cls_features = features['x_norm_clstoken']
        return self.classifier(cls_features)

def main():
    print("=" * 60)
    print("DINOv2 Minimal Training Script")
    print("=" * 60)
    
    # 超参数
    batch_size = 32
    num_epochs = 10  # 减少epoch数用于测试
    learning_rate = 0.001
    
    # 输出路径
    csv_file = '/home/<USER>/Cytofm/JinWooChoi/minimal_train_performance.csv'
    model_file = '/home/<USER>/Cytofm/JinWooChoi/minimal_dinov2_model.pth'
    
    # 创建输出目录
    os.makedirs(os.path.dirname(csv_file), exist_ok=True)
    
    # 数据变换
    train_transform = transforms.Compose([
        transforms.Resize((224, 224)),
        transforms.RandomHorizontalFlip(),
        transforms.ToTensor(),
        transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
    ])
    
    val_transform = transforms.Compose([
        transforms.Resize((224, 224)),
        transforms.ToTensor(),
        transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
    ])
    
    # 创建数据集
    print("Loading datasets...")
    train_dataset = CervixDataset(
        '/home/<USER>/JinWooChoi/train.txt',
        '/home/<USER>/JinWooChoi/class_mapping.txt',
        transform=train_transform
    )
    
    val_dataset = CervixDataset(
        '/home/<USER>/JinWooChoi/val.txt',
        '/home/<USER>/JinWooChoi/class_mapping.txt',
        transform=val_transform
    )
    
    # 数据加载器
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=2)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=2)
    
    num_classes = len(set(train_dataset.labels))
    print(f"Number of classes: {num_classes}")
    print(f"Training samples: {len(train_dataset)}")
    print(f"Validation samples: {len(val_dataset)}")
    
    # 创建模型
    model = SimpleDINOv2Classifier(num_classes=num_classes, backbone_name='vit_base')
    model.to(device)
    
    # 优化器和损失函数
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.Adam(model.classifier.parameters(), lr=learning_rate)
    
    # 训练记录
    with open(csv_file, 'w', newline='') as f:
        writer = csv.writer(f)
        writer.writerow(['Epoch', 'Train Loss', 'Train Acc', 'Val Loss', 'Val Acc'])
    
    print(f"\nStarting training for {num_epochs} epochs...")
    
    # 训练循环
    for epoch in range(num_epochs):
        # 训练
        model.train()
        train_loss = 0.0
        train_correct = 0
        train_total = 0
        
        for batch_idx, (images, labels) in enumerate(train_loader):
            images, labels = images.to(device), labels.to(device)
            
            optimizer.zero_grad()
            outputs = model(images)
            loss = criterion(outputs, labels)
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
            _, predicted = torch.max(outputs, 1)
            train_total += labels.size(0)
            train_correct += (predicted == labels).sum().item()
            
            if batch_idx % 10 == 0:
                print(f'Epoch {epoch+1}/{num_epochs}, Batch {batch_idx}/{len(train_loader)}, Loss: {loss.item():.4f}')
        
        train_loss /= len(train_loader)
        train_acc = 100 * train_correct / train_total
        
        # 验证
        model.eval()
        val_loss = 0.0
        val_correct = 0
        val_total = 0
        
        with torch.no_grad():
            for images, labels in val_loader:
                images, labels = images.to(device), labels.to(device)
                outputs = model(images)
                loss = criterion(outputs, labels)
                
                val_loss += loss.item()
                _, predicted = torch.max(outputs, 1)
                val_total += labels.size(0)
                val_correct += (predicted == labels).sum().item()
        
        val_loss /= len(val_loader)
        val_acc = 100 * val_correct / val_total
        
        print(f'Epoch {epoch+1}/{num_epochs}: Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.2f}%, Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.2f}%')
        
        # 保存记录
        with open(csv_file, 'a', newline='') as f:
            writer = csv.writer(f)
            writer.writerow([epoch+1, train_loss, train_acc, val_loss, val_acc])
    
    # 保存模型
    torch.save(model.state_dict(), model_file)
    print(f"\nModel saved to: {model_file}")
    print(f"Training log saved to: {csv_file}")
    
    print("\n" + "=" * 60)
    print("Training completed successfully!")
    print("=" * 60)

if __name__ == "__main__":
    main()

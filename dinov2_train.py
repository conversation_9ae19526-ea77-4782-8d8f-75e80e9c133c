#!/usr/bin/env python3
"""
DINOv2训练脚本 - 使用配置文件版本
基于vit_all.py的结构，将ViT替换为DINOv2
"""

import torch.optim as optim
import csv
import os
from PIL import Image
import torch
import torch.nn as nn
import torchvision.transforms as transforms
from torch.utils.data import DataLoader
from sklearn.metrics import classification_report, accuracy_score, roc_auc_score, confusion_matrix
from sklearn.metrics import precision_score, recall_score, f1_score
import numpy as np
import sys
import time
from datetime import datetime

# 导入配置和DINOv2模块
from dinov2_config import *
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
import dinov2
from dinov2 import vision_transformer as vits

def main():
    """主训练函数"""
    
    # 打印配置信息
    print_config()
    
    # 创建输出目录
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    
    # Custom dataset class
    class CervixDataset(torch.utils.data.Dataset):
        def __init__(self, txt_file, class_mapping_file, transform=None):
            self.data = []
            self.labels = []
            self.transform = transform
            self.classes = []

            # Load class mapping
            class_mapping = {}
            with open(class_mapping_file, 'r') as f:
                for line in f:
                    parts = line.strip().split()
                    if len(parts) >= 2:
                        class_name = ' '.join(parts[:-1])
                        idx = int(parts[-1])
                        class_mapping[class_name] = idx
                        while len(self.classes) <= idx:
                            self.classes.append("")
                        self.classes[idx] = class_name

            # Read TXT file
            with open(txt_file, 'r') as f:
                for line in f:
                    parts = line.strip().split()
                    if len(parts) > 1:
                        img_path = parts[0]
                        class_label = ' '.join(parts[1:])
                        class_idx = class_mapping[class_label]
                        self.data.append(img_path)
                        self.labels.append(class_idx)

        def __len__(self):
            return len(self.data)

        def __getitem__(self, idx):
            img_path = self.data[idx]
            label = self.labels[idx]

            if not os.path.exists(img_path):
                raise FileNotFoundError(f"Image not found: {img_path}")

            image = Image.open(img_path).convert('RGB')
            if self.transform:
                image = self.transform(image)
            return image, label

    # DINOv2分类器
    class DINOv2Classifier(nn.Module):
        def __init__(self, backbone_name, num_classes, freeze_backbone=True, 
                     pretrained_path=None, dropout_rate=0.5):
            super().__init__()
            
            # 创建backbone
            if backbone_name == 'vit_small':
                self.backbone = vits.vit_small(patch_size=14)
            elif backbone_name == 'vit_base':
                self.backbone = vits.vit_base(patch_size=14)
            elif backbone_name == 'vit_large':
                self.backbone = vits.vit_large(patch_size=14)
            elif backbone_name == 'vit_giant2':
                self.backbone = vits.vit_giant2(patch_size=14)
            else:
                raise ValueError(f"Unsupported backbone: {backbone_name}")
            
            # 加载预训练权重
            if pretrained_path and os.path.exists(pretrained_path):
                print(f"Loading pretrained weights from {pretrained_path}")
                try:
                    self.backbone, embed_dim = dinov2.build_model(
                        device=DEVICE, gpu_num=1, model_name='dinov2_vitl', ckpt_path=pretrained_path
                    )
                except Exception as e:
                    print(f"Failed to load pretrained weights: {e}")
            
            # 冻结backbone
            if freeze_backbone:
                for param in self.backbone.parameters():
                    param.requires_grad = False
                print("DINOv2 backbone frozen")
            
            # 分类头
            embed_dim = self.backbone.embed_dim
            self.classifier = nn.Sequential(
                nn.Dropout(p=dropout_rate),
                nn.Linear(embed_dim, 512),
                nn.ReLU(),
                nn.Dropout(p=dropout_rate/2),
                nn.Linear(512, num_classes)
            )
            
            print(f"DINOv2 Classifier: {backbone_name}, embed_dim={embed_dim}, classes={num_classes}")
        
        def forward(self, x):
            if not any(p.requires_grad for p in self.backbone.parameters()):
                with torch.no_grad():
                    features = self.backbone.forward_features(x)
                    cls_features = features['x_norm_clstoken']
            else:
                features = self.backbone.forward_features(x)
                cls_features = features['x_norm_clstoken']
            
            return self.classifier(cls_features)

    # 数据预处理
    train_transform = transforms.Compose([
        transforms.Resize((IMAGE_SIZE, IMAGE_SIZE)),
        transforms.RandomHorizontalFlip(p=TRAIN_AUGMENTATION['horizontal_flip_prob']),
        transforms.RandomRotation(TRAIN_AUGMENTATION['rotation_degrees']),
        transforms.ColorJitter(**TRAIN_AUGMENTATION['color_jitter']),
        transforms.ToTensor(),
        transforms.Normalize(IMAGENET_MEAN, IMAGENET_STD)
    ])

    val_transform = transforms.Compose([
        transforms.Resize((IMAGE_SIZE, IMAGE_SIZE)),
        transforms.ToTensor(),
        transforms.Normalize(IMAGENET_MEAN, IMAGENET_STD)
    ])

    # 创建数据集
    print("Loading datasets...")
    train_dataset = CervixDataset(TRAIN_TXT_PATH, CLASS_MAPPING_PATH, train_transform)
    val_dataset = CervixDataset(VAL_TXT_PATH, CLASS_MAPPING_PATH, val_transform)
    test_dataset = CervixDataset(TEST_TXT_PATH, CLASS_MAPPING_PATH, val_transform)

    # 数据加载器
    train_loader = DataLoader(train_dataset, batch_size=BATCH_SIZE, shuffle=True, num_workers=NUM_WORKERS)
    val_loader = DataLoader(val_dataset, batch_size=BATCH_SIZE, shuffle=False, num_workers=NUM_WORKERS)
    test_loader = DataLoader(test_dataset, batch_size=BATCH_SIZE, shuffle=False, num_workers=NUM_WORKERS)

    num_classes = len(set(train_dataset.labels))
    print(f"Classes: {num_classes}, Train: {len(train_dataset)}, Val: {len(val_dataset)}, Test: {len(test_dataset)}")

    # 创建模型
    model = DINOv2Classifier(
        backbone_name=BACKBONE_NAME,
        num_classes=num_classes,
        freeze_backbone=FREEZE_BACKBONE,
        pretrained_path=PRETRAINED_PATH,
        dropout_rate=DROPOUT_RATE
    ).to(DEVICE)

    # 优化器和调度器
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.Adam(
        filter(lambda p: p.requires_grad, model.parameters()), 
        lr=LEARNING_RATE, weight_decay=WEIGHT_DECAY
    )
    scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=NUM_EPOCHS)

    # 训练记录
    train_losses, val_losses = [], []
    train_accuracies, val_accuracies = [], []
    best_val_acc = 0.0

    # CSV记录
    with open(CSV_FILE_PATH, mode='w', newline='') as file:
        writer = csv.writer(file)
        writer.writerow(['Epoch', 'Train Loss', 'Train Accuracy', 'Val Loss', 'Val Accuracy'])

    print(f"\nStarting training for {NUM_EPOCHS} epochs...")
    start_time = time.time()

    # 训练循环
    for epoch in range(NUM_EPOCHS):
        # 训练阶段
        model.train()
        running_loss = 0.0
        correct = 0
        total = 0

        for batch_idx, (images, labels) in enumerate(train_loader):
            images, labels = images.to(DEVICE), labels.to(DEVICE)
            
            optimizer.zero_grad()
            outputs = model(images)
            loss = criterion(outputs, labels)
            loss.backward()
            optimizer.step()

            running_loss += loss.item()
            _, predicted = torch.max(outputs.data, 1)
            total += labels.size(0)
            correct += (predicted == labels).sum().item()

            if batch_idx % PRINT_FREQ == 0:
                print(f'Epoch [{epoch+1}/{NUM_EPOCHS}], Batch [{batch_idx}/{len(train_loader)}], Loss: {loss.item():.4f}')

        train_loss = running_loss / len(train_loader)
        train_accuracy = 100 * correct / total

        # 验证阶段
        model.eval()
        running_loss = 0.0
        correct = 0
        total = 0

        with torch.no_grad():
            for images, labels in val_loader:
                images, labels = images.to(DEVICE), labels.to(DEVICE)
                outputs = model(images)
                loss = criterion(outputs, labels)
                running_loss += loss.item()
                _, predicted = torch.max(outputs.data, 1)
                total += labels.size(0)
                correct += (predicted == labels).sum().item()

        val_loss = running_loss / len(val_loader)
        val_accuracy = 100 * correct / total

        # 记录
        train_losses.append(train_loss)
        train_accuracies.append(train_accuracy)
        val_losses.append(val_loss)
        val_accuracies.append(val_accuracy)

        print(f'Epoch [{epoch + 1}/{NUM_EPOCHS}], Train Loss: {train_loss:.4f}, Train Acc: {train_accuracy:.2f}%, Val Loss: {val_loss:.4f}, Val Acc: {val_accuracy:.2f}%')

        # 保存CSV
        with open(CSV_FILE_PATH, mode='a', newline='') as file:
            writer = csv.writer(file)
            writer.writerow([epoch + 1, train_loss, train_accuracy, val_loss, val_accuracy])

        # 保存最佳模型
        if SAVE_BEST_MODEL and val_accuracy > best_val_acc:
            best_val_acc = val_accuracy
            torch.save(model.state_dict(), BEST_MODEL_PATH)
            print(f"Best model saved with validation accuracy: {best_val_acc:.2f}%")

        scheduler.step()

    # 保存最终模型
    torch.save(model.state_dict(), MODEL_SAVE_PATH)
    training_time = time.time() - start_time
    print(f"\nTraining completed in {training_time/3600:.2f} hours")
    print(f"Model saved to: {MODEL_SAVE_PATH}")

    # 开始评估
    print(f"\n{'='*60}")
    print("Starting evaluation...")
    print(f"{'='*60}")

    # 加载最佳模型进行评估
    if SAVE_BEST_MODEL and os.path.exists(BEST_MODEL_PATH):
        model.load_state_dict(torch.load(BEST_MODEL_PATH))
        print(f"Loaded best model for evaluation")
    else:
        model.load_state_dict(torch.load(MODEL_SAVE_PATH))
        print(f"Loaded final model for evaluation")

    model.eval()

    # 测试评估
    all_preds = []
    all_labels = []
    all_probs = []

    with torch.no_grad():
        for images, labels in test_loader:
            images, labels = images.to(DEVICE), labels.to(DEVICE)
            outputs = model(images)
            _, preds = torch.max(outputs, 1)
            probs = torch.nn.functional.softmax(outputs, dim=1)

            all_preds.extend(preds.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())
            all_probs.append(probs.cpu().numpy())

    all_probs = np.vstack(all_probs)
    all_preds = np.array(all_preds)
    all_labels = np.array(all_labels)

    # 基本评估指标
    accuracy = accuracy_score(all_labels, all_preds)
    macro_precision = precision_score(all_labels, all_preds, average='macro')
    macro_recall = recall_score(all_labels, all_preds, average='macro')
    macro_f1 = f1_score(all_labels, all_preds, average='macro')

    print(f"\nDINOv2 Evaluation Results:")
    print(f"Accuracy: {accuracy:.4f}")
    print(f"Macro Precision: {macro_precision:.4f}")
    print(f"Macro Recall: {macro_recall:.4f}")
    print(f"Macro F1: {macro_f1:.4f}")

    # 分类报告
    print("\nClassification Report:")
    print(classification_report(all_labels, all_preds, target_names=test_dataset.classes))

    # 保存结果
    if SAVE_RESULTS:
        results_file = MODEL_SAVE_PATH.replace('.pth', '_results.txt')
        with open(results_file, 'w') as f:
            f.write(f"DINOv2 Model Evaluation Results\n")
            f.write(f"Model: {BACKBONE_NAME}\n")
            f.write(f"Training time: {training_time/3600:.2f} hours\n")
            f.write(f"Accuracy: {accuracy:.4f}\n")
            f.write(f"Macro Precision: {macro_precision:.4f}\n")
            f.write(f"Macro Recall: {macro_recall:.4f}\n")
            f.write(f"Macro F1: {macro_f1:.4f}\n")
            f.write(f"\nClassification Report:\n")
            f.write(classification_report(all_labels, all_preds, target_names=test_dataset.classes))

        print(f"Results saved to: {results_file}")

    print(f"\n{'='*60}")
    print("DINOv2 training and evaluation completed!")
    print(f"{'='*60}")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
DINOv2 模型完整使用示例
展示如何导入、加载和使用DINOv2模型进行特征提取
"""

import torch
import torch.nn as nn
from PIL import Image
import numpy as np
from torchvision import transforms
import sys
import os

# 添加当前目录到Python路径，以便导入dinov2模块
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入DINOv2模块
try:
    # 方法1: 直接导入整个模块
    import dinov2
    from dinov2 import vision_transformer as vits
    print("✅ 成功导入dinov2模块")
except ImportError as e:
    print(f"❌ 导入dinov2模块失败: {e}")
    print("请确保dinov2文件夹在当前目录下")
    sys.exit(1)

def create_model_without_pretrained():
    """
    创建不使用预训练权重的模型（用于演示）
    """
    print("\n=== 创建模型（无预训练权重）===")
    
    # 创建ViT Large模型
    model = vits.vit_large(
        patch_size=14,
        img_size=224,
        init_values=1.0e-05,
        ffn_layer='mlp',  # 或 'swiglufused'
        block_chunks=4,
        qkv_bias=True,
        proj_bias=True,
        ffn_bias=True,
    )
    
    print(f"模型创建成功")
    print(f"嵌入维度: {model.embed_dim}")
    print(f"补丁大小: {model.patch_size}")
    print(f"层数: {model.n_blocks}")
    
    return model

def create_model_with_pretrained(ckpt_path, model_name='dinov2_vitl'):
    """
    使用预训练权重创建模型
    
    Args:
        ckpt_path: 预训练权重文件路径
        model_name: 模型名称
    """
    print(f"\n=== 加载预训练模型: {model_name} ===")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    try:
        model, embed_dim = dinov2.build_model(
            device=device,
            gpu_num=1,
            model_name=model_name,
            ckpt_path=ckpt_path
        )
        print(f"✅ 预训练模型加载成功，嵌入维度: {embed_dim}")
        return model, embed_dim
    except Exception as e:
        print(f"❌ 加载预训练模型失败: {e}")
        return None, None

def create_data_transforms():
    """
    创建数据预处理管道
    """
    print("\n=== 创建数据预处理 ===")
    
    # 方法1: 使用dinov2提供的transform
    transform_dinov2 = dinov2.build_transform()
    
    # 方法2: 手动创建transform（与dinov2.build_transform()等效）
    transform_manual = transforms.Compose([
        transforms.Resize((224, 224), interpolation=transforms.InterpolationMode.BICUBIC),
        transforms.ToTensor(),
        transforms.Normalize(
            mean=(0.485, 0.456, 0.406),  # ImageNet标准化参数
            std=(0.229, 0.224, 0.225)
        )
    ])
    
    print("✅ 数据预处理管道创建成功")
    return transform_dinov2

def extract_features_demo(model, device='cpu'):
    """
    特征提取演示
    """
    print(f"\n=== 特征提取演示 (设备: {device}) ===")
    
    model.to(device)
    model.eval()
    
    # 创建示例输入数据
    batch_size = 2
    dummy_images = torch.randn(batch_size, 3, 224, 224).to(device)
    print(f"输入图像形状: {dummy_images.shape}")
    
    with torch.no_grad():
        # 方法1: 使用forward_features获取详细特征
        features = model.forward_features(dummy_images)
        
        print("特征提取结果:")
        print(f"  - CLS token特征形状: {features['x_norm_clstoken'].shape}")
        print(f"  - Patch token特征形状: {features['x_norm_patchtokens'].shape}")
        
        # 方法2: 直接forward获取CLS token特征
        cls_output = model(dummy_images)
        print(f"  - 直接输出形状: {cls_output.shape}")
        
        # 方法3: 获取中间层特征
        intermediate_features = model.get_intermediate_layers(
            dummy_images, 
            n=4,  # 获取最后4层的特征
            return_class_token=True
        )
        print(f"  - 中间层特征数量: {len(intermediate_features)}")
        for i, (patch_feat, cls_feat) in enumerate(intermediate_features):
            print(f"    层 {i}: patch {patch_feat.shape}, cls {cls_feat.shape}")
    
    return features

def process_real_image_demo(model, transform, image_path=None):
    """
    处理真实图像的演示
    """
    print(f"\n=== 真实图像处理演示 ===")
    
    if image_path and os.path.exists(image_path):
        # 处理真实图像
        try:
            image = Image.open(image_path).convert('RGB')
            print(f"加载图像: {image_path}, 尺寸: {image.size}")
            
            # 预处理
            image_tensor = transform(image).unsqueeze(0)  # 添加batch维度
            
            # 提取特征
            device = next(model.parameters()).device
            image_tensor = image_tensor.to(device)
            
            with torch.no_grad():
                features = model.forward_features(image_tensor)
                print(f"✅ 图像特征提取成功")
                print(f"  - CLS特征: {features['x_norm_clstoken'].shape}")
                print(f"  - Patch特征: {features['x_norm_patchtokens'].shape}")
                
            return features
            
        except Exception as e:
            print(f"❌ 处理图像失败: {e}")
    else:
        print("⚠️  未提供有效图像路径，跳过真实图像处理")
    
    return None

def main():
    """
    主函数 - 完整的使用流程演示
    """
    print("🚀 DINOv2 模型使用演示开始")
    print("=" * 50)
    
    # 1. 检查设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 2. 创建模型（无预训练权重版本）
    model = create_model_without_pretrained()
    
    # 3. 如果有预训练权重，可以这样加载：
    # pretrained_model_path = "path/to/your/pretrained_model.pth"
    # if os.path.exists(pretrained_model_path):
    #     model, embed_dim = create_model_with_pretrained(pretrained_model_path)
    
    # 4. 创建数据预处理
    transform = create_data_transforms()
    
    # 5. 特征提取演示
    features = extract_features_demo(model, device)
    
    # 6. 真实图像处理演示（如果有图像文件）
    # image_path = "path/to/your/image.jpg"  # 替换为实际图像路径
    # process_real_image_demo(model, transform, image_path)
    
    print("\n" + "=" * 50)
    print("✅ DINOv2 模型使用演示完成")
    
    # 7. 模型信息总结
    print(f"\n📊 模型信息总结:")
    print(f"  - 模型类型: DINOv2 Vision Transformer")
    print(f"  - 嵌入维度: {model.embed_dim}")
    print(f"  - 补丁大小: {model.patch_size}x{model.patch_size}")
    print(f"  - 层数: {model.n_blocks}")
    print(f"  - 注意力头数: {model.num_heads}")
    print(f"  - 参数量: {sum(p.numel() for p in model.parameters()):,}")

if __name__ == "__main__":
    main()

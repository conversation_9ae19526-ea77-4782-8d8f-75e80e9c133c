#!/usr/bin/env python3
"""
使用修复后的vision_transformer的DINOv2训练脚本
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torchvision.transforms as transforms
from torch.utils.data import DataLoader
from PIL import Image
import os
import sys
import importlib.util
import csv
from sklearn.metrics import classification_report, accuracy_score
import numpy as np

# 设备配置
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"Using device: {device}")

def import_fixed_vision_transformer():
    """导入修复后的vision_transformer"""
    
    fixed_file = '/home/<USER>/dinov2/JinWooChoi/vision_transformer_fixed.py'
    
    if not os.path.exists(fixed_file):
        print(f"❌ Fixed vision_transformer not found: {fixed_file}")
        print("Please run: python fix_relative_imports.py first")
        sys.exit(1)
    
    try:
        # 添加路径
        sys.path.insert(0, '/home/<USER>/dinov2/JinWooChoi')
        
        # 导入修复后的模块
        spec = importlib.util.spec_from_file_location("vision_transformer_fixed", fixed_file)
        vits = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(vits)
        
        print("✓ Fixed vision_transformer imported successfully")
        return vits
        
    except Exception as e:
        print(f"❌ Failed to import fixed vision_transformer: {e}")
        sys.exit(1)

# 导入vision_transformer
print("Importing fixed vision_transformer...")
vits = import_fixed_vision_transformer()

# 数据集类
class CervixDataset(torch.utils.data.Dataset):
    def __init__(self, txt_file, class_mapping_file, transform=None):
        self.data = []
        self.labels = []
        self.transform = transform
        self.classes = []

        # 加载类别映射
        class_mapping = {}
        with open(class_mapping_file, 'r') as f:
            for line in f:
                parts = line.strip().split()
                if len(parts) >= 2:
                    class_name = ' '.join(parts[:-1])
                    idx = int(parts[-1])
                    class_mapping[class_name] = idx
                    while len(self.classes) <= idx:
                        self.classes.append("")
                    self.classes[idx] = class_name

        # 读取数据文件
        with open(txt_file, 'r') as f:
            for line in f:
                parts = line.strip().split()
                if len(parts) > 1:
                    img_path = parts[0]
                    class_label = ' '.join(parts[1:])
                    class_idx = class_mapping[class_label]
                    self.data.append(img_path)
                    self.labels.append(class_idx)

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):
        img_path = self.data[idx]
        label = self.labels[idx]

        if not os.path.exists(img_path):
            raise FileNotFoundError(f"Image not found: {img_path}")

        image = Image.open(img_path).convert('RGB')
        if self.transform:
            image = self.transform(image)
        return image, label

# DINOv2分类器
class DINOv2Classifier(nn.Module):
    def __init__(self, num_classes=10, backbone_name='vit_base', freeze_backbone=True):
        super().__init__()
        
        print(f"Creating {backbone_name} backbone...")
        
        # 创建backbone
        try:
            if backbone_name == 'vit_small':
                self.backbone = vits.vit_small(patch_size=14)
            elif backbone_name == 'vit_base':
                self.backbone = vits.vit_base(patch_size=14)
            elif backbone_name == 'vit_large':
                self.backbone = vits.vit_large(patch_size=14)
            elif backbone_name == 'vit_giant2':
                self.backbone = vits.vit_giant2(patch_size=14)
            else:
                print(f"Unknown backbone {backbone_name}, using vit_base")
                self.backbone = vits.vit_base(patch_size=14)
        except Exception as e:
            print(f"Error creating {backbone_name}: {e}")
            print("Trying vit_base as fallback...")
            self.backbone = vits.vit_base(patch_size=14)
            backbone_name = 'vit_base'
        
        # 冻结backbone
        if freeze_backbone:
            for param in self.backbone.parameters():
                param.requires_grad = False
            print("✓ Backbone frozen")
        else:
            print("✓ Backbone will be fine-tuned")
        
        # 分类头
        embed_dim = self.backbone.embed_dim
        self.classifier = nn.Sequential(
            nn.Dropout(0.5),
            nn.Linear(embed_dim, 512),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(512, num_classes)
        )
        
        print(f"✓ Model created:")
        print(f"  - Backbone: {backbone_name}")
        print(f"  - Embed dim: {embed_dim}")
        print(f"  - Num classes: {num_classes}")
    
    def forward(self, x):
        # 提取特征
        if not any(p.requires_grad for p in self.backbone.parameters()):
            with torch.no_grad():
                features = self.backbone.forward_features(x)
                cls_features = features['x_norm_clstoken']
        else:
            features = self.backbone.forward_features(x)
            cls_features = features['x_norm_clstoken']
        
        return self.classifier(cls_features)

def main():
    print("=" * 60)
    print("DINOv2 Training with Fixed Import")
    print("=" * 60)
    
    # 超参数
    batch_size = 32
    num_epochs = 50
    learning_rate = 0.001
    
    # 输出路径
    csv_file = '/home/<USER>/Cytofm/JinWooChoi/fixed_train_performance.csv'
    model_file = '/home/<USER>/Cytofm/JinWooChoi/fixed_dinov2_model.pth'
    
    # 创建输出目录
    os.makedirs(os.path.dirname(csv_file), exist_ok=True)
    
    # 数据变换
    
    
    val_transform = transforms.Compose([
        transforms.Resize((224, 224)),
        transforms.ToTensor(),
        transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
    ])
    
    # 创建数据集
    print("Loading datasets...")
    train_dataset = CervixDataset(
        '/home/<USER>/JinWooChoi/train.txt',
        '/home/<USER>/JinWooChoi/class_mapping.txt',
        transform=train_transform
    )
    
    val_dataset = CervixDataset(
        '/home/<USER>/JinWooChoi/val.txt',
        '/home/<USER>/JinWooChoi/class_mapping.txt',
        transform=val_transform
    )
    
    test_dataset = CervixDataset(
        '/home/<USER>/JinWooChoi/test.txt',
        '/home/<USER>/JinWooChoi/class_mapping.txt',
        transform=val_transform
    )
    
    # 数据加载器
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=4)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=4)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False, num_workers=4)
    
    num_classes = len(set(train_dataset.labels))
    print(f"Number of classes: {num_classes}")
    print(f"Training samples: {len(train_dataset)}")
    print(f"Validation samples: {len(val_dataset)}")
    print(f"Test samples: {len(test_dataset)}")
    
    # 创建模型
    model = DINOv2Classifier(
        num_classes=num_classes, 
        backbone_name='vit_large',
        freeze_backbone=True
    )
    model.to(device)
    
    # 优化器和损失函数
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.Adam(
        filter(lambda p: p.requires_grad, model.parameters()), 
        lr=learning_rate,
        weight_decay=1e-4
    )
    scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=num_epochs)
    
    # 训练记录
    with open(csv_file, 'w', newline='') as f:
        writer = csv.writer(f)
        writer.writerow(['Epoch', 'Train Loss', 'Train Acc', 'Val Loss', 'Val Acc'])
    
    print(f"\nStarting training for {num_epochs} epochs...")
    
    # 训练循环
    for epoch in range(num_epochs):
        # 训练
        model.train()
        train_loss = 0.0
        train_correct = 0
        train_total = 0
        
        for batch_idx, (images, labels) in enumerate(train_loader):
            images, labels = images.to(device), labels.to(device)
            
            optimizer.zero_grad()
            outputs = model(images)
            loss = criterion(outputs, labels)
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
            _, predicted = torch.max(outputs, 1)
            train_total += labels.size(0)
            train_correct += (predicted == labels).sum().item()
            
            if batch_idx % 20 == 0:
                print(f'Epoch {epoch+1}/{num_epochs}, Batch {batch_idx}/{len(train_loader)}, Loss: {loss.item():.4f}')
        
        train_loss /= len(train_loader)
        train_acc = 100 * train_correct / train_total
        
        # 验证
        model.eval()
        val_loss = 0.0
        val_correct = 0
        val_total = 0
        
        with torch.no_grad():
            for images, labels in val_loader:
                images, labels = images.to(device), labels.to(device)
                outputs = model(images)
                loss = criterion(outputs, labels)
                
                val_loss += loss.item()
                _, predicted = torch.max(outputs, 1)
                val_total += labels.size(0)
                val_correct += (predicted == labels).sum().item()
        
        val_loss /= len(val_loader)
        val_acc = 100 * val_correct / val_total
        
        print(f'Epoch {epoch+1}/{num_epochs}: Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.2f}%, Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.2f}%')
        
        # 保存记录
        with open(csv_file, 'a', newline='') as f:
            writer = csv.writer(f)
            writer.writerow([epoch+1, train_loss, train_acc, val_loss, val_acc])
        
        scheduler.step()
    
    # 保存模型
    torch.save(model.state_dict(), model_file)
    print(f"\nModel saved to: {model_file}")

 
# 开始test部分
model.load_state_dict(torch.load(model_save_path))
model.eval()

# Get class names for evaluation
class_names = test_dataset.classes

# 初始化存储预测和真实标签的列表
all_preds = []
all_labels = []
all_probs = []  # 初始化存储概率的列表

# 测试模型
with torch.no_grad():
    for images, labels in test_loader:
        images, labels = images.to(device), labels.to(device)  # 移动到设备
        outputs = model(images)
        _, preds = torch.max(outputs, 1)

        # 获取概率输出
        probs = torch.nn.functional.softmax(outputs, dim=1)

        all_preds.extend(preds.cpu().numpy())  # 将预测结果移回 CPU
        all_labels.extend(labels.cpu().numpy())  # 将真实标签移回 CPU
        all_probs.append(probs.cpu().numpy())  # 将概率结果移回 CPU

# 将概率列表转换为numpy数组
all_probs = np.vstack(all_probs)

# 计算准确率、召回率和 F1 分数
all_preds = np.array(all_preds)
all_labels = np.array(all_labels)

# 生成分类报告
report = classification_report(all_labels, all_preds, target_names=class_names, output_dict=True, zero_division=0)
print(classification_report(all_labels, all_preds, target_names=class_names))

# 计算混淆矩阵
cm = confusion_matrix(all_labels, all_preds)
print("Confusion Matrix:")
print(cm)

# 计算每个类别的指标
print("\n===== 每个类别的评估指标 (DINOv2) =====")
n_classes = len(class_names)
class_accuracies = []
sensitivities = []
specificities = []
precisions = []  # 精确率列表
f1_scores = []  # F1分数列表
auc_scores = []

# 将标签转换为one-hot编码用于AUC计算

y_true_bin = label_binarize(all_labels, classes=range(n_classes))

for i in range(n_classes):
    # 获取当前类别的样本索引
    class_indices = np.where(all_labels == i)[0]

    if len(class_indices) > 0:
        # 计算当前类别的Top-1准确率
        class_accuracy = accuracy_score(all_labels[class_indices], all_preds[class_indices])
        class_accuracies.append(class_accuracy)
        print(f"Accuracy for {class_names[i]}: {class_accuracy:.4f}")

        # 计算敏感性(Sensitivity/Recall) - 即真阳性率(TPR)
        TP = cm[i, i]  # 真阳性
        FN = np.sum(cm[i, :]) - TP  # 假阴性
        sensitivity = TP / (TP + FN) if (TP + FN) > 0 else 0
        sensitivities.append(sensitivity)
        print(f"Sensitivity for {class_names[i]}: {sensitivity:.4f}")

        # 计算特异性(Specificity)
        FP = np.sum(cm[:, i]) - TP  # 假阳性
        TN = np.sum(cm) - TP - FP - FN  # 真阴性
        specificity = TN / (TN + FP) if (TN + FP) > 0 else 0
        specificities.append(specificity)
        print(f"Specificity for {class_names[i]}: {specificity:.4f}")

        # 计算精确率(Precision)
        precision = TP / (TP + FP) if (TP + FP) > 0 else 0
        precisions.append(precision)
        print(f"Precision for {class_names[i]}: {precision:.4f}")

        # 计算F1分数 - 精确率和召回率的调和平均值
        f1 = 2 * (precision * sensitivity) / (precision + sensitivity) if (precision + sensitivity) > 0 else 0
        f1_scores.append(f1)
        print(f"F1 Score for {class_names[i]}: {f1:.4f}")

        # 计算AUC
        if len(np.unique(y_true_bin[:, i])) > 1:  # 确保有正负样本
            auc = roc_auc_score(y_true_bin[:, i], all_probs[:, i])
            auc_scores.append(auc)
            print(f"AUC for {class_names[i]}: {auc:.4f}")
        else:
            print(f"AUC for {class_names[i]}: N/A (only one class present)")
    else:
        print(f"No samples for class {class_names[i]}")
    print("-" * 50)

# 计算全数据的宏观指标
print("\n===== 全数据宏观评估指标 (DINOv2) =====")

# 计算 Top-1 准确率
top1_accuracy = accuracy_score(all_labels, all_preds)
print(f'Overall Accuracy: {top1_accuracy:.4f}')

# 计算宏观精确率、召回率和F1分数
macro_precision = precision_score(all_labels, all_preds, average='macro')
macro_recall = recall_score(all_labels, all_preds, average='macro')
macro_f1 = f1_score(all_labels, all_preds, average='macro')

print(f"Macro-averaged Precision: {macro_precision:.4f}")
print(f"Macro-averaged Recall/Sensitivity: {macro_recall:.4f}")
print(f"Macro-averaged F1 Score: {macro_f1:.4f}")

# 计算微观精确率、召回率和F1分数
micro_precision = precision_score(all_labels, all_preds, average='micro')
micro_recall = recall_score(all_labels, all_preds, average='micro')
micro_f1 = f1_score(all_labels, all_preds, average='micro')

print(f"Micro-averaged Precision: {micro_precision:.4f}")
print(f"Micro-averaged Recall/Sensitivity: {micro_recall:.4f}")
print(f"Micro-averaged F1 Score: {micro_f1:.4f}")

# 计算宏观特异性(Macro-averaged Specificity)
macro_specificity = np.mean(specificities)
print(f"Macro-averaged Specificity: {macro_specificity:.4f}")

# 计算自定义宏观精确率和F1分数
custom_macro_precision = np.mean(precisions)
custom_macro_f1 = np.mean(f1_scores)
print(f"Custom Macro-averaged Precision: {custom_macro_precision:.4f}")
print(f"Custom Macro-averaged F1 Score: {custom_macro_f1:.4f}")

# 计算宏观AUC(Macro-averaged AUC)
if auc_scores:
    macro_auc = np.mean(auc_scores)
    print(f"Macro-averaged AUC: {macro_auc:.4f}")

# 计算微观AUC(Micro-averaged AUC)
try:
    # 对于多类别问题，微观AUC通常使用OvR(One-vs-Rest)策略
    micro_auc = roc_auc_score(y_true_bin, all_probs, average='micro')
    print(f"Micro-averaged AUC: {micro_auc:.4f}")
except:
    print("Could not calculate Micro-averaged AUC")

# 汇总所有宏观指标
print(f"\n{'='*60}")
print(f"DINOv2 宏观评估指标汇总")
print(f"{'='*60}")
print(f"Accuracy: {top1_accuracy:.4f}")
print(f"Macro-averaged Precision: {macro_precision:.4f}")
print(f"Macro-averaged Recall/Sensitivity: {macro_recall:.4f}")
print(f"Macro-averaged F1 Score: {macro_f1:.4f}")
print(f"Macro-averaged Specificity: {macro_specificity:.4f}")
if 'macro_auc' in locals():
    print(f"Macro-averaged AUC: {macro_auc:.4f}")
if 'micro_auc' in locals():
    print(f"Micro-averaged AUC: {micro_auc:.4f}")

# 保存评估结果到文件
results_file = model_save_path.replace('.pth', '_results.txt')
with open(results_file, 'w') as f:
    f.write("DINOv2 Model Evaluation Results\n")
    f.write("="*50 + "\n")
    f.write(f"Overall Accuracy: {top1_accuracy:.4f}\n")
    f.write(f"Macro-averaged Precision: {macro_precision:.4f}\n")
    f.write(f"Macro-averaged Recall: {macro_recall:.4f}\n")
    f.write(f"Macro-averaged F1 Score: {macro_f1:.4f}\n")
    f.write(f"Macro-averaged Specificity: {macro_specificity:.4f}\n")
    if 'macro_auc' in locals():
        f.write(f"Macro-averaged AUC: {macro_auc:.4f}\n")
    if 'micro_auc' in locals():
        f.write(f"Micro-averaged AUC: {micro_auc:.4f}\n")

print(f"\nResults saved to: {results_file}")
print(f"Training log saved to: {csv_file_path}")
print(f"\n{'='*60}")
print(f"DINOv2 training and evaluation completed!")
print(f"{'='*60}")
#!/usr/bin/env python3
"""
DINOv2 快速入门示例
最简单的使用方式
"""

import torch
import sys
import os

# 导入DINOv2模块
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
import dinov2
from dinov2 import vision_transformer as vits

def quick_demo():
    """快速演示"""
    print("🚀 DINOv2 快速入门")
    
    # 1. 创建模型
    model = vits.vit_large(patch_size=14)
    model.eval()
    print(f"✅ 模型创建成功，嵌入维度: {model.embed_dim}")
    
    # 2. 创建示例输入
    x = torch.randn(1, 3, 224, 224)  # [batch_size, channels, height, width]
    print(f"📥 输入形状: {x.shape}")
    
    # 3. 前向推理
    with torch.no_grad():
        # 获取CLS token特征（全局特征）
        output = model(x)
        print(f"📤 输出特征形状: {output.shape}")
        
        # 获取详细特征
        features = model.forward_features(x)
        print(f"🔍 CLS token: {features['x_norm_clstoken'].shape}")
        print(f"🔍 Patch tokens: {features['x_norm_patchtokens'].shape}")
    
    print("✅ 完成！")

if __name__ == "__main__":
    quick_demo()

import torch.optim as optim
import csv
import os
from PIL import Image
import torch
import torch.nn as nn
import torchvision.transforms as transforms
from torch.utils.data import DataLoader
from sklearn.metrics import classification_report, accuracy_score, roc_auc_score, confusion_matrix
from sklearn.metrics import precision_score, recall_score, f1_score
import numpy as np
import timm

# 判断是否可用GPU
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

# 定义超参数
learning_rate = 0.001  # 可以根据需要调整
batch_size = 32
num_epochs = 50
csv_file_path = r'/home/<USER>/JinWooChoi/Vit/train_performance.csv'
model_save_path = r'/home/<USER>/JinWooChoi/Vit/trained_vit_model.pth'


# Custom dataset class
class CervixDataset(torch.utils.data.Dataset):
    def __init__(self, txt_file, class_mapping_file, transform=None):
        self.data = []
        self.labels = []
        self.transform = transform
        self.classes = []  # List to store class names

        # Load class mapping - fix the split to use space instead of tab
        class_mapping = {}
        with open(class_mapping_file, 'r') as f:
            for line in f:
                parts = line.strip().split()
                if len(parts) >= 2:
                    class_name = ' '.join(parts[:-1])  # Handle multi-word class names
                    idx = int(parts[-1])
                    class_mapping[class_name] = idx
                    # Store class names in order of their indices
                    while len(self.classes) <= idx:
                        self.classes.append("")
                    self.classes[idx] = class_name

        # Read TXT file
        with open(txt_file, 'r') as f:
            for line in f:
                parts = line.strip().split()
                if len(parts) > 1:
                    img_path = parts[0]
                    class_label = ' '.join(parts[1:])
                    class_idx = class_mapping[class_label]

                    self.data.append(img_path)
                    self.labels.append(class_idx)

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):
        img_path = self.data[idx]
        label = self.labels[idx]

        # Ensure image path is valid
        if not os.path.exists(img_path):
            raise FileNotFoundError(f"Image not found: {img_path}")

        # Load image
        image = Image.open(img_path).convert('RGB')

        if self.transform:
            image = self.transform(image)

        return image, label



# Data transformations
train_transform = transforms.Compose([
    transforms.Resize((224, 224)),
    transforms.RandomHorizontalFlip(),
    transforms.RandomRotation(10),
    transforms.ToTensor(),
    transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
])

val_transform = transforms.Compose([
    transforms.Resize((224, 224)),
    transforms.ToTensor(),
    transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
])

test_transform = transforms.Compose([
    transforms.Resize((224, 224)),
    transforms.ToTensor(),
    transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
])

# Create datasets directly from the prepared train.txt and val.txt files
train_dataset = CervixDataset(
    '/home/<USER>/JinWooChoi/train.txt',
    '/home/<USER>/JinWooChoi/class_mapping.txt',
    transform=train_transform
)

val_dataset = CervixDataset(
    '/home/<USER>/JinWooChoi/val.txt',
    '/home/<USER>/JinWooChoi/class_mapping.txt',
    transform=val_transform
)

test_dataset = CervixDataset(
    '/home/<USER>/JinWooChoi/test.txt',
    '/home/<USER>/JinWooChoi/class_mapping.txt',
    transform=test_transform
)

# Create data loaders
train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=4)
val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=4)
test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False, num_workers=4)

# Get number of classes
num_classes = len(set(train_dataset.labels))

# 定义 Vision Transformer 模型
model = timm.create_model('vit_base_patch16_224', pretrained=True)  # 使用预训练的 Vision Transformer

# 修改分类器以添加 Dropout
model.head = nn.Sequential(
    nn.Dropout(p=0.5),  # 添加 Dropout 层
    nn.Linear(model.head.in_features, num_classes)  # 修改最后的分类层
)


# 将模型移动到设备
model.to(device)

# 选择损失函数和优化器
criterion = nn.CrossEntropyLoss()
optimizer = optim.Adam(model.parameters(), lr=learning_rate, weight_decay=1e-4)

# 使用余弦退火学习率调度器
scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=num_epochs)

# 记录损失和准确率
train_losses = []
val_losses = []
train_accuracies = []
val_accuracies = []

# CSV 文件写入
with open(csv_file_path, mode='w', newline='') as file:
    writer = csv.writer(file)
    writer.writerow(['Epoch', 'Train Loss', 'Train Accuracy', 'Val Loss', 'Val Accuracy'])  # 写入表头

# 训练模型
for epoch in range(num_epochs):
    model.train()
    running_loss = 0.0
    correct = 0
    total = 0

    for images, labels in train_loader:
        images, labels = images.to(device), labels.to(device)  # 移动到设备
        optimizer.zero_grad()
        outputs = model(images)
        loss = criterion(outputs, labels)
        loss.backward()
        optimizer.step()

        running_loss += loss.item()
        _, predicted = torch.max(outputs.data, 1)
        total += labels.size(0)
        correct += (predicted == labels).sum().item()

    train_loss = running_loss / len(train_loader)
    train_accuracy = 100 * correct / total
    train_losses.append(train_loss)
    train_accuracies.append(train_accuracy)

    # 验证模型
    model.eval()
    running_loss = 0.0
    correct = 0
    total = 0

    with torch.no_grad():
        for images, labels in val_loader:
            images, labels = images.to(device), labels.to(device)  # 移动到设备
            outputs = model(images)
            loss = criterion(outputs, labels)
            running_loss += loss.item()
            _, predicted = torch.max(outputs.data, 1)
            total += labels.size(0)
            correct += (predicted == labels).sum().item()

    val_loss = running_loss / len(val_loader)
    val_accuracy = 100 * correct / total
    val_losses.append(val_loss)
    val_accuracies.append(val_accuracy)

    print(f'Epoch [{epoch + 1}/{num_epochs}], '
          f'Train Loss: {train_loss:.4f}, Train Accuracy: {train_accuracy:.2f}%, '
          f'Val Loss: {val_loss:.4f}, Val Accuracy: {val_accuracy:.2f}%')

    # 保存训练过程数据到 CSV
    with open(csv_file_path, mode='a', newline='') as file:
        writer = csv.writer(file)
        writer.writerow([epoch + 1, train_loss, train_accuracy, val_loss, val_accuracy])

    # 更新学习率
    scheduler.step()

# 保存训练好的模型
torch.save(model.state_dict(), model_save_path)

# 开始test部分
model.load_state_dict(torch.load(model_save_path))
model.eval()

# Get class names for evaluation
class_names = test_dataset.classes


# 初始化存储预测和真实标签的列表
all_preds = []
all_labels = []
all_probs = []  # 初始化存储概率的列表

# 测试模型
with torch.no_grad():
    for images, labels in test_loader:
        images, labels = images.to(device), labels.to(device)  # 移动到设备
        outputs = model(images)
        _, preds = torch.max(outputs, 1)

        # 获取概率输出
        probs = torch.nn.functional.softmax(outputs, dim=1)

        all_preds.extend(preds.cpu().numpy())  # 将预测结果移回 CPU
        all_labels.extend(labels.cpu().numpy())  # 将真实标签移回 CPU
        all_probs.append(probs.cpu().numpy())  # 将概率结果移回 CPU

# 将概率列表转换为numpy数组
all_probs = np.vstack(all_probs)

# 计算准确率、召回率和 F1 分数
all_preds = np.array(all_preds)
all_labels = np.array(all_labels)


# 生成分类报告
report = classification_report(all_labels, all_preds, target_names=class_names, output_dict=True, zero_division=0)
print(classification_report(all_labels, all_preds, target_names=class_names))

# 计算混淆矩阵
cm = confusion_matrix(all_labels, all_preds)
print("Confusion Matrix:")
print(cm)

# 计算每个类别的指标
print("\n===== 每个类别的评估指标 =====")
n_classes = len(class_names)
class_accuracies = []
sensitivities = []
specificities = []
precisions = []  # 精确率列表
f1_scores = []  # F1分数列表
auc_scores = []

# 将标签转换为one-hot编码用于AUC计算
from sklearn.preprocessing import label_binarize

y_true_bin = label_binarize(all_labels, classes=range(n_classes))

for i in range(n_classes):
    # 获取当前类别的样本索引
    class_indices = np.where(all_labels == i)[0]

    if len(class_indices) > 0:
        # 计算当前类别的Top-1准确率
        class_accuracy = accuracy_score(all_labels[class_indices], all_preds[class_indices])
        class_accuracies.append(class_accuracy)
        print(f"Accuracy for {class_names[i]}: {class_accuracy:.4f}")

        # 计算敏感性(Sensitivity/Recall) - 即真阳性率(TPR)
        TP = cm[i, i]  # 真阳性
        FN = np.sum(cm[i, :]) - TP  # 假阴性
        sensitivity = TP / (TP + FN) if (TP + FN) > 0 else 0
        sensitivities.append(sensitivity)
        print(f"Sensitivity for {class_names[i]}: {sensitivity:.4f}")

        # 计算特异性(Specificity)
        FP = np.sum(cm[:, i]) - TP  # 假阳性
        TN = np.sum(cm) - TP - FP - FN  # 真阴性
        specificity = TN / (TN + FP) if (TN + FP) > 0 else 0
        specificities.append(specificity)
        print(f"Specificity for {class_names[i]}: {specificity:.4f}")

        # 计算精确率(Precision)
        precision = TP / (TP + FP) if (TP + FP) > 0 else 0
        precisions.append(precision)
        print(f"Precision for {class_names[i]}: {precision:.4f}")

        # 计算F1分数 - 精确率和召回率的调和平均值
        f1 = 2 * (precision * sensitivity) / (precision + sensitivity) if (precision + sensitivity) > 0 else 0
        f1_scores.append(f1)
        print(f"F1 Score for {class_names[i]}: {f1:.4f}")

        # 计算AUC
        if len(np.unique(y_true_bin[:, i])) > 1:  # 确保有正负样本
            auc = roc_auc_score(y_true_bin[:, i], all_probs[:, i])
            auc_scores.append(auc)
            print(f"AUC for {class_names[i]}: {auc:.4f}")
        else:
            print(f"AUC for {class_names[i]}: N/A (only one class present)")
    else:
        print(f"No samples for class {class_names[i]}")
    print("-" * 50)

# 计算全数据的宏观指标
print("\n===== 全数据宏观评估指标 =====")

# 计算 Top-1 准确率
top1_accuracy = accuracy_score(all_labels, all_preds)
print(f'Overall Accuracy: {top1_accuracy:.4f}')

# 计算宏观精确率、召回率和F1分数
macro_precision = precision_score(all_labels, all_preds, average='macro')
macro_recall = recall_score(all_labels, all_preds, average='macro')
macro_f1 = f1_score(all_labels, all_preds, average='macro')

print(f"Macro-averaged Precision: {macro_precision:.4f}")
print(f"Macro-averaged Recall/Sensitivity: {macro_recall:.4f}")
print(f"Macro-averaged F1 Score: {macro_f1:.4f}")

# 计算微观精确率、召回率和F1分数
micro_precision = precision_score(all_labels, all_preds, average='micro')
micro_recall = recall_score(all_labels, all_preds, average='micro')
micro_f1 = f1_score(all_labels, all_preds, average='micro')

print(f"Micro-averaged Precision: {micro_precision:.4f}")
print(f"Micro-averaged Recall/Sensitivity: {micro_recall:.4f}")
print(f"Micro-averaged F1 Score: {micro_f1:.4f}")

# 计算宏观特异性(Macro-averaged Specificity)
macro_specificity = np.mean(specificities)
print(f"Macro-averaged Specificity: {macro_specificity:.4f}")

# 计算自定义宏观精确率和F1分数
custom_macro_precision = np.mean(precisions)
custom_macro_f1 = np.mean(f1_scores)
print(f"Custom Macro-averaged Precision: {custom_macro_precision:.4f}")
print(f"Custom Macro-averaged F1 Score: {custom_macro_f1:.4f}")

# 计算宏观AUC(Macro-averaged AUC)
if auc_scores:
    macro_auc = np.mean(auc_scores)
    print(f"Macro-averaged AUC: {macro_auc:.4f}")

# 计算微观AUC(Micro-averaged AUC)
try:
    # 对于多类别问题，微观AUC通常使用OvR(One-vs-Rest)策略
    micro_auc = roc_auc_score(y_true_bin, all_probs, average='micro')
    print(f"Micro-averaged AUC: {micro_auc:.4f}")
except:
    print("Could not calculate Micro-averaged AUC")

# 汇总所有宏观指标
print("\n===== 宏观评估指标汇总 =====")
print(f"Accuracy: {top1_accuracy:.4f}")
print(f"Macro-averaged Precision: {macro_precision:.4f}")
print(f"Macro-averaged Recall/Sensitivity: {macro_recall:.4f}")
print(f"Macro-averaged F1 Score: {macro_f1:.4f}")
print(f"Macro-averaged Specificity: {macro_specificity:.4f}")
if 'macro_auc' in locals():
    print(f"Macro-averaged AUC: {macro_auc:.4f}")
if 'micro_auc' in locals():
    print(f"Micro-averaged AUC: {micro_auc:.4f}")

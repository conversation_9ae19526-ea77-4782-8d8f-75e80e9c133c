#!/usr/bin/env python3
"""
DINOv2快速启动脚本
提供简单的命令行界面来运行不同的任务
"""

import os
import sys
import argparse
import subprocess

def run_setup_test():
    """运行设置测试"""
    print("Running DINOv2 setup test...")
    try:
        result = subprocess.run([sys.executable, "test_dinov2_setup.py"], 
                              capture_output=False, text=True)
        return result.returncode == 0
    except Exception as e:
        print(f"Error running setup test: {e}")
        return False

def run_training(script_type="config"):
    """运行训练"""
    if script_type == "config":
        script_name = "dinov2_train.py"
        print("Running DINOv2 training with configuration file...")
    else:
        script_name = "dinov2_all.py"
        print("Running DINOv2 training with complete script...")
    
    try:
        result = subprocess.run([sys.executable, script_name], 
                              capture_output=False, text=True)
        return result.returncode == 0
    except Exception as e:
        print(f"Error running training: {e}")
        return False

def show_config():
    """显示当前配置"""
    try:
        from dinov2_config import print_config
        print_config()
    except Exception as e:
        print(f"Error loading configuration: {e}")

def check_data_files():
    """检查数据文件是否存在"""
    try:
        from dinov2_config import (TRAIN_TXT_PATH, VAL_TXT_PATH, 
                                  TEST_TXT_PATH, CLASS_MAPPING_PATH)
        
        files_to_check = [
            ("Training data", TRAIN_TXT_PATH),
            ("Validation data", VAL_TXT_PATH),
            ("Test data", TEST_TXT_PATH),
            ("Class mapping", CLASS_MAPPING_PATH)
        ]
        
        print("Checking data files...")
        all_exist = True
        
        for name, path in files_to_check:
            if os.path.exists(path):
                print(f"✓ {name}: {path}")
            else:
                print(f"✗ {name}: {path} (NOT FOUND)")
                all_exist = False
        
        if all_exist:
            print("✓ All data files found!")
        else:
            print("❌ Some data files are missing. Please check the paths in dinov2_config.py")
        
        return all_exist
    except Exception as e:
        print(f"Error checking data files: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description="DINOv2 Training Runner")
    parser.add_argument("command", choices=["test", "train", "config", "check-data", "help"],
                       help="Command to run")
    parser.add_argument("--script-type", choices=["config", "complete"], default="config",
                       help="Training script type (default: config)")
    
    args = parser.parse_args()
    
    if args.command == "test":
        print("=" * 60)
        print("Running DINOv2 Setup Test")
        print("=" * 60)
        success = run_setup_test()
        if success:
            print("\n🎉 Setup test completed successfully!")
        else:
            print("\n❌ Setup test failed!")
    
    elif args.command == "train":
        print("=" * 60)
        print("Starting DINOv2 Training")
        print("=" * 60)
        
        # 首先检查数据文件
        if not check_data_files():
            print("\n❌ Data files check failed. Please fix the paths before training.")
            return
        
        success = run_training(args.script_type)
        if success:
            print("\n🎉 Training completed successfully!")
        else:
            print("\n❌ Training failed!")
    
    elif args.command == "config":
        print("=" * 60)
        print("Current DINOv2 Configuration")
        print("=" * 60)
        show_config()
    
    elif args.command == "check-data":
        print("=" * 60)
        print("Checking Data Files")
        print("=" * 60)
        check_data_files()
    
    elif args.command == "help":
        print("=" * 60)
        print("DINOv2 Training Help")
        print("=" * 60)
        print("Available commands:")
        print("  test        - Run setup test to verify environment")
        print("  train       - Start training (use --script-type to choose version)")
        print("  config      - Show current configuration")
        print("  check-data  - Check if data files exist")
        print("  help        - Show this help message")
        print()
        print("Examples:")
        print("  python run_dinov2.py test")
        print("  python run_dinov2.py train")
        print("  python run_dinov2.py train --script-type complete")
        print("  python run_dinov2.py config")
        print("  python run_dinov2.py check-data")
        print()
        print("Files:")
        print("  dinov2_train.py    - Training with configuration file")
        print("  dinov2_all.py      - Complete training script")
        print("  dinov2_config.py   - Configuration file")
        print("  test_dinov2_setup.py - Setup test script")

if __name__ == "__main__":
    main()

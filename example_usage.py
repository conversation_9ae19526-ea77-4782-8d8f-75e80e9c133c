#!/usr/bin/env python3
"""
DINOv2 模型使用示例
这个脚本展示了如何使用DINOv2模型进行特征提取和推理
"""

import torch
import torch.nn as nn
from PIL import Image
import numpy as np
from torchvision import transforms
import dinov2  # 假设当前目录就是dinov2模块

def load_pretrained_model(model_name='dinov2_vitl', ckpt_path=None, device='cuda'):
    """
    加载预训练的DINOv2模型
    
    Args:
        model_name: 模型名称 ('dinov2_vitl', 'gpfm', 'ccs')
        ckpt_path: 预训练权重路径
        device: 设备 ('cuda' 或 'cpu')
    
    Returns:
        model: 加载好的模型
        embed_dim: 嵌入维度
    """
    if ckpt_path is None:
        # 如果没有提供权重路径，创建一个随机初始化的模型
        print("警告: 没有提供预训练权重，使用随机初始化的模型")
        if model_name == 'dinov2_vitl':
            vit_kwargs = dict(
                img_size=224,
                patch_size=14,
                init_values=1.0e-05,
                ffn_layer='swiglufused',
                block_chunks=4,
                qkv_bias=True,
                proj_bias=True,
                ffn_bias=True,
            )
            model = dinov2.vision_transformer.vit_large(**vit_kwargs)
        else:
            raise NotImplementedError(f'{model_name} 未实现')
        
        model.to(device)
        model.eval()
        return model, model.embed_dim
    else:
        # 使用提供的build_model函数
        return dinov2.build_model(device, 1, model_name, ckpt_path)

def create_data_transform():
    """
    创建数据预处理管道
    """
    return dinov2.build_transform()

def extract_features(model, image_tensor, device='cuda'):
    """
    从图像中提取特征
    
    Args:
        model: DINOv2模型
        image_tensor: 预处理后的图像张量
        device: 设备
    
    Returns:
        features: 提取的特征
    """
    image_tensor = image_tensor.to(device)
    
    with torch.no_grad():
        # 获取特征
        features = model.forward_features(image_tensor)
        
        # 返回CLS token的特征（全局特征）和patch特征
        cls_features = features["x_norm_clstoken"]  # [B, embed_dim]
        patch_features = features["x_norm_patchtokens"]  # [B, num_patches, embed_dim]
        
    return {
        'cls_features': cls_features,
        'patch_features': patch_features,
        'all_features': features
    }

def process_single_image(image_path, model, transform, device='cuda'):
    """
    处理单张图像
    
    Args:
        image_path: 图像路径
        model: DINOv2模型
        transform: 数据预处理
        device: 设备
    
    Returns:
        features: 提取的特征
    """
    # 加载图像
    image = Image.open(image_path).convert('RGB')
    
    # 预处理
    image_tensor = transform(image).unsqueeze(0)  # 添加batch维度
    
    # 提取特征
    features = extract_features(model, image_tensor, device)
    
    return features

def main():
    """
    主函数 - 演示如何使用DINOv2模型
    """
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 1. 加载模型
    print("正在加载模型...")
    model, embed_dim = load_pretrained_model(
        model_name='dinov2_vitl',
        ckpt_path=None,  # 如果有预训练权重，在这里提供路径
        device=device
    )
    print(f"模型加载完成，嵌入维度: {embed_dim}")
    
    # 2. 创建数据预处理
    transform = create_data_transform()
    
    # 3. 创建示例数据（如果没有真实图像）
    print("创建示例数据...")
    dummy_image = torch.randn(1, 3, 224, 224).to(device)
    
    # 4. 提取特征
    print("提取特征...")
    features = extract_features(model, dummy_image, device)
    
    print(f"CLS特征形状: {features['cls_features'].shape}")
    print(f"Patch特征形状: {features['patch_features'].shape}")
    
    # 5. 如果有真实图像，可以这样处理：
    # features = process_single_image('path/to/your/image.jpg', model, transform, device)
    
    print("特征提取完成！")

if __name__ == "__main__":
    main()

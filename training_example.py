#!/usr/bin/env python3
"""
DINOv2 训练示例
展示如何使用DINOv2进行下游任务的微调训练
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
from torchvision import transforms
import sys
import os

# 导入DINOv2模块
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
import dinov2
from dinov2 import vision_transformer as vits

class SimpleClassifier(nn.Module):
    """
    基于DINOv2的简单分类器
    """
    def __init__(self, backbone, num_classes=10, freeze_backbone=True):
        super().__init__()
        self.backbone = backbone
        self.num_classes = num_classes
        
        # 是否冻结backbone参数
        if freeze_backbone:
            for param in self.backbone.parameters():
                param.requires_grad = False
        
        # 分类头
        self.classifier = nn.Sequential(
            nn.Linear(backbone.embed_dim, 512),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(512, num_classes)
        )
    
    def forward(self, x):
        # 提取特征
        with torch.no_grad() if hasattr(self, '_freeze_backbone') else torch.enable_grad():
            features = self.backbone.forward_features(x)
            cls_features = features['x_norm_clstoken']  # [B, embed_dim]
        
        # 分类
        logits = self.classifier(cls_features)
        return logits

class DummyDataset(Dataset):
    """
    示例数据集（用于演示）
    """
    def __init__(self, num_samples=1000, num_classes=10):
        self.num_samples = num_samples
        self.num_classes = num_classes
        self.transform = transforms.Compose([
            transforms.ToPILImage(),
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], 
                               std=[0.229, 0.224, 0.225])
        ])
    
    def __len__(self):
        return self.num_samples
    
    def __getitem__(self, idx):
        # 生成随机图像和标签
        image = torch.randint(0, 256, (3, 224, 224), dtype=torch.uint8)
        image = self.transform(image)
        label = torch.randint(0, self.num_classes, (1,)).item()
        return image, label

def create_model_and_classifier(num_classes=10, freeze_backbone=True):
    """
    创建模型和分类器
    """
    print("🏗️  创建模型...")
    
    # 创建DINOv2 backbone
    backbone = vits.vit_large(patch_size=14)
    print(f"✅ Backbone创建成功，嵌入维度: {backbone.embed_dim}")
    
    # 创建分类器
    classifier = SimpleClassifier(
        backbone=backbone, 
        num_classes=num_classes,
        freeze_backbone=freeze_backbone
    )
    
    print(f"✅ 分类器创建成功，类别数: {num_classes}")
    print(f"🔒 Backbone冻结: {freeze_backbone}")
    
    return classifier

def train_one_epoch(model, dataloader, criterion, optimizer, device):
    """
    训练一个epoch
    """
    model.train()
    total_loss = 0
    correct = 0
    total = 0
    
    for batch_idx, (data, target) in enumerate(dataloader):
        data, target = data.to(device), target.to(device)
        
        # 前向传播
        optimizer.zero_grad()
        output = model(data)
        loss = criterion(output, target)
        
        # 反向传播
        loss.backward()
        optimizer.step()
        
        # 统计
        total_loss += loss.item()
        pred = output.argmax(dim=1)
        correct += pred.eq(target).sum().item()
        total += target.size(0)
        
        if batch_idx % 10 == 0:
            print(f'  Batch {batch_idx:3d}: Loss={loss.item():.4f}, '
                  f'Acc={100.*correct/total:.2f}%')
    
    avg_loss = total_loss / len(dataloader)
    accuracy = 100. * correct / total
    
    return avg_loss, accuracy

def validate(model, dataloader, criterion, device):
    """
    验证模型
    """
    model.eval()
    total_loss = 0
    correct = 0
    total = 0
    
    with torch.no_grad():
        for data, target in dataloader:
            data, target = data.to(device), target.to(device)
            output = model(data)
            loss = criterion(output, target)
            
            total_loss += loss.item()
            pred = output.argmax(dim=1)
            correct += pred.eq(target).sum().item()
            total += target.size(0)
    
    avg_loss = total_loss / len(dataloader)
    accuracy = 100. * correct / total
    
    return avg_loss, accuracy

def main():
    """
    主训练函数
    """
    print("🚀 DINOv2 训练示例开始")
    print("=" * 50)
    
    # 设置参数
    num_classes = 10
    batch_size = 8
    num_epochs = 3
    learning_rate = 0.001
    freeze_backbone = True  # 是否冻结backbone
    
    # 设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 1. 创建模型
    model = create_model_and_classifier(num_classes, freeze_backbone)
    model.to(device)
    
    # 2. 创建数据集和数据加载器
    print("\n📊 创建数据集...")
    train_dataset = DummyDataset(num_samples=200, num_classes=num_classes)
    val_dataset = DummyDataset(num_samples=50, num_classes=num_classes)
    
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
    
    print(f"✅ 训练集: {len(train_dataset)} 样本")
    print(f"✅ 验证集: {len(val_dataset)} 样本")
    
    # 3. 设置损失函数和优化器
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.Adam(model.parameters(), lr=learning_rate)
    
    print(f"\n⚙️  训练配置:")
    print(f"  - 学习率: {learning_rate}")
    print(f"  - 批次大小: {batch_size}")
    print(f"  - 训练轮数: {num_epochs}")
    
    # 4. 训练循环
    print(f"\n🏃 开始训练...")
    for epoch in range(num_epochs):
        print(f"\nEpoch {epoch+1}/{num_epochs}")
        print("-" * 30)
        
        # 训练
        train_loss, train_acc = train_one_epoch(
            model, train_loader, criterion, optimizer, device
        )
        
        # 验证
        val_loss, val_acc = validate(model, val_loader, criterion, device)
        
        print(f"训练 - Loss: {train_loss:.4f}, Acc: {train_acc:.2f}%")
        print(f"验证 - Loss: {val_loss:.4f}, Acc: {val_acc:.2f}%")
    
    print(f"\n✅ 训练完成!")
    
    # 5. 保存模型
    torch.save(model.state_dict(), 'dinov2_classifier.pth')
    print("💾 模型已保存为 'dinov2_classifier.pth'")

if __name__ == "__main__":
    main()

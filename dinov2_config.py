"""
DINOv2训练配置文件
在这里修改训练参数和路径设置
"""

import os

# ==================== 路径配置 ====================
# 数据路径
TRAIN_TXT_PATH = '/home/<USER>/JinWooChoi/train.txt'
VAL_TXT_PATH = '/home/<USER>/JinWooChoi/val.txt'
TEST_TXT_PATH = '/home/<USER>/JinWooChoi/test.txt'
CLASS_MAPPING_PATH = '/home/<USER>/JinWooChoi/class_mapping.txt'

# 输出路径
OUTPUT_DIR = '/home/<USER>/JinWooChoi/DINOv2'
CSV_FILE_PATH = os.path.join(OUTPUT_DIR, 'train_performance.csv')
MODEL_SAVE_PATH = os.path.join(OUTPUT_DIR, 'trained_dinov2_model.pth')

# 预训练权重路径（可选）
PRETRAINED_PATH = None  # 如果有预训练权重，在这里指定路径
# PRETRAINED_PATH = '/path/to/dinov2_pretrained_weights.pth'

# ==================== 模型配置 ====================
# DINOv2 backbone选择: 'vit_small', 'vit_base', 'vit_large', 'vit_giant2'
BACKBONE_NAME = 'vit_large'

# 是否冻结backbone参数（True: 只训练分类头, False: 端到端训练）
FREEZE_BACKBONE = True

# Dropout率
DROPOUT_RATE = 0.5

# ==================== 训练配置 ====================
# 训练超参数
LEARNING_RATE = 0.001
BATCH_SIZE = 32
NUM_EPOCHS = 50
WEIGHT_DECAY = 1e-4

# 数据加载器配置
NUM_WORKERS = 4

# ==================== 数据增强配置 ====================
# 图像尺寸
IMAGE_SIZE = 224

# 训练时数据增强参数
TRAIN_AUGMENTATION = {
    'horizontal_flip_prob': 0.5,
    'rotation_degrees': 10,
    'color_jitter': {
        'brightness': 0.2,
        'contrast': 0.2,
        'saturation': 0.2,
        'hue': 0.1
    }
}

# ImageNet标准化参数
IMAGENET_MEAN = [0.485, 0.456, 0.406]
IMAGENET_STD = [0.229, 0.224, 0.225]

# ==================== 设备配置 ====================
# 自动检测GPU
import torch
DEVICE = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

# ==================== 日志配置 ====================
# 训练过程中打印频率
PRINT_FREQ = 10  # 每10个batch打印一次

# 是否保存最佳模型
SAVE_BEST_MODEL = True
BEST_MODEL_PATH = os.path.join(OUTPUT_DIR, 'best_dinov2_model.pth')

# ==================== 评估配置 ====================
# 是否计算每个类别的详细指标
DETAILED_EVALUATION = True

# 是否保存评估结果到文件
SAVE_RESULTS = True

# ==================== 模型规格信息 ====================
MODEL_SPECS = {
    'vit_small': {
        'embed_dim': 384,
        'depth': 12,
        'num_heads': 6,
        'approx_params': '22M'
    },
    'vit_base': {
        'embed_dim': 768,
        'depth': 12,
        'num_heads': 12,
        'approx_params': '86M'
    },
    'vit_large': {
        'embed_dim': 1024,
        'depth': 24,
        'num_heads': 16,
        'approx_params': '307M'
    },
    'vit_giant2': {
        'embed_dim': 1536,
        'depth': 40,
        'num_heads': 24,
        'approx_params': '1.1B'
    }
}

def print_config():
    """打印当前配置"""
    print("=" * 60)
    print("DINOv2 Training Configuration")
    print("=" * 60)
    print(f"Backbone: {BACKBONE_NAME}")
    print(f"Model specs: {MODEL_SPECS.get(BACKBONE_NAME, 'Unknown')}")
    print(f"Freeze backbone: {FREEZE_BACKBONE}")
    print(f"Learning rate: {LEARNING_RATE}")
    print(f"Batch size: {BATCH_SIZE}")
    print(f"Epochs: {NUM_EPOCHS}")
    print(f"Device: {DEVICE}")
    print(f"Output directory: {OUTPUT_DIR}")
    if PRETRAINED_PATH:
        print(f"Pretrained weights: {PRETRAINED_PATH}")
    else:
        print("Pretrained weights: None (random initialization)")
    print("=" * 60)

if __name__ == "__main__":
    print_config()

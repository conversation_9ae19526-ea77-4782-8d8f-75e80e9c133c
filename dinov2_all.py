import torch.optim as optim
import csv
import os
from PIL import Image
import torch
import torch.nn as nn
import torchvision.transforms as transforms
from torch.utils.data import DataLoader
from sklearn.metrics import classification_report, accuracy_score, roc_auc_score, confusion_matrix
from sklearn.metrics import precision_score, recall_score, f1_score
from sklearn.preprocessing import label_binarize
import numpy as np
import sys

# 添加dinov2模块路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# 尝试导入dinov2模块
try:
    import dinov2
    from dinov2 import vision_transformer as vits
    print("✓ DINOv2 modules imported successfully")
except ImportError as e:
    print(f"❌ Error importing dinov2: {e}")
    print(f"Current directory: {current_dir}")
    print(f"Python path: {sys.path[:3]}...")  # 显示前3个路径

    # 检查dinov2目录是否存在
    dinov2_path = os.path.join(current_dir, 'dinov2')
    if os.path.exists(dinov2_path):
        print(f"✓ dinov2 directory found at: {dinov2_path}")
        # 列出dinov2目录内容
        try:
            files = os.listdir(dinov2_path)
            print(f"dinov2 directory contents: {files[:5]}...")  # 显示前5个文件
        except:
            pass
    else:
        print(f"❌ dinov2 directory not found at: {dinov2_path}")
        print("Please ensure the dinov2 directory is in the same folder as dinov2_all.py")

    # 尝试直接导入vision_transformer
    try:
        sys.path.insert(0, dinov2_path)
        from vision_transformer import *
        print("✓ Imported vision_transformer directly")
        # 创建一个简单的命名空间来模拟dinov2模块
        class DummyDinov2:
            pass
        dinov2 = DummyDinov2()
        import vision_transformer as vits
    except ImportError as e2:
        print(f"❌ Failed to import vision_transformer directly: {e2}")
        print("Please check if the dinov2 module files are present and accessible.")
        sys.exit(1)

# 判断是否可用GPU
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"Using device: {device}")

# 定义超参数
learning_rate = 0.001  # 可以根据需要调整
batch_size = 32
num_epochs = 50
csv_file_path = r'/home/<USER>/Cytofm/JinWooChoi/train_performance.csv'
model_save_path = r'/home/<USER>/Cytofm/JinWooChoi/trained_dinov2_model.pth'

# 创建保存目录
os.makedirs(os.path.dirname(csv_file_path), exist_ok=True)
os.makedirs(os.path.dirname(model_save_path), exist_ok=True)


# Custom dataset class (与原版相同)
class CervixDataset(torch.utils.data.Dataset):
    def __init__(self, txt_file, class_mapping_file, transform=None):
        self.data = []
        self.labels = []
        self.transform = transform
        self.classes = []  # List to store class names

        # Load class mapping - fix the split to use space instead of tab
        class_mapping = {}
        with open(class_mapping_file, 'r') as f:
            for line in f:
                parts = line.strip().split()
                if len(parts) >= 2:
                    class_name = ' '.join(parts[:-1])  # Handle multi-word class names
                    idx = int(parts[-1])
                    class_mapping[class_name] = idx
                    # Store class names in order of their indices
                    while len(self.classes) <= idx:
                        self.classes.append("")
                    self.classes[idx] = class_name

        # Read TXT file
        with open(txt_file, 'r') as f:
            for line in f:
                parts = line.strip().split()
                if len(parts) > 1:
                    img_path = parts[0]
                    class_label = ' '.join(parts[1:])
                    class_idx = class_mapping[class_label]

                    self.data.append(img_path)
                    self.labels.append(class_idx)

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):
        img_path = self.data[idx]
        label = self.labels[idx]

        # Ensure image path is valid
        if not os.path.exists(img_path):
            raise FileNotFoundError(f"Image not found: {img_path}")

        # Load image
        image = Image.open(img_path).convert('RGB')

        if self.transform:
            image = self.transform(image)

        return image, label


# DINOv2分类器类
class DINOv2Classifier(nn.Module):
    """
    基于DINOv2的分类器
    """
    def __init__(self, backbone_name='vit_large', num_classes=10, freeze_backbone=True, 
                 pretrained_path=None, dropout_rate=0.5):
        super().__init__()
        
        # 创建DINOv2 backbone
        if backbone_name == 'vit_small':
            self.backbone = vits.vit_small(patch_size=14)
        elif backbone_name == 'vit_base':
            self.backbone = vits.vit_base(patch_size=14)
        elif backbone_name == 'vit_large':
            self.backbone = vits.vit_large(patch_size=14)
        elif backbone_name == 'vit_giant2':
            self.backbone = vits.vit_giant2(patch_size=14)
        else:
            raise ValueError(f"Unsupported backbone: {backbone_name}")
        
        # 加载预训练权重（如果提供）
        if pretrained_path and os.path.exists(pretrained_path):
            print(f"Loading pretrained weights from {pretrained_path}")
            try:
                # 使用dinov2的build_model函数加载预训练权重
                self.backbone, embed_dim = dinov2.build_model(
                    device=device,
                    gpu_num=1,
                    model_name='dinov2_vitl',  # 根据需要调整
                    ckpt_path=pretrained_path
                )
            except Exception as e:
                print(f"Failed to load pretrained weights: {e}")
                print("Using randomly initialized weights")
        
        # 是否冻结backbone参数
        if freeze_backbone:
            for param in self.backbone.parameters():
                param.requires_grad = False
            print("DINOv2 backbone frozen")
        else:
            print("DINOv2 backbone will be fine-tuned")
        
        # 获取嵌入维度
        embed_dim = self.backbone.embed_dim
        
        # 分类头
        self.classifier = nn.Sequential(
            nn.Dropout(p=dropout_rate),
            nn.Linear(embed_dim, 512),
            nn.ReLU(),
            nn.Dropout(p=dropout_rate/2),
            nn.Linear(512, num_classes)
        )
        
        print(f"DINOv2 Classifier created:")
        print(f"  - Backbone: {backbone_name}")
        print(f"  - Embed dim: {embed_dim}")
        print(f"  - Num classes: {num_classes}")
        print(f"  - Dropout rate: {dropout_rate}")
    
    def forward(self, x):
        # 提取特征
        if hasattr(self, '_freeze_backbone') or not any(p.requires_grad for p in self.backbone.parameters()):
            with torch.no_grad():
                features = self.backbone.forward_features(x)
                cls_features = features['x_norm_clstoken']  # [B, embed_dim]
        else:
            features = self.backbone.forward_features(x)
            cls_features = features['x_norm_clstoken']  # [B, embed_dim]
        
        # 分类
        logits = self.classifier(cls_features)
        return logits


# Data transformations (与DINOv2兼容的数据预处理)
train_transform = transforms.Compose([
    transforms.Resize((224, 224)),
    transforms.RandomHorizontalFlip(),
    transforms.RandomRotation(10),
    transforms.ColorJitter(brightness=0.2, contrast=0.2, saturation=0.2, hue=0.1),
    transforms.ToTensor(),
    transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])  # ImageNet标准化
])

val_transform = transforms.Compose([
    transforms.Resize((224, 224)),
    transforms.ToTensor(),
    transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
])

test_transform = transforms.Compose([
    transforms.Resize((224, 224)),
    transforms.ToTensor(),
    transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
])

# Create datasets directly from the prepared train.txt and val.txt files
train_dataset = CervixDataset(
    '/home/<USER>/JinWooChoi/train.txt',
    '/home/<USER>/JinWooChoi/class_mapping.txt',
    transform=train_transform
)

val_dataset = CervixDataset(
    '/home/<USER>/JinWooChoi/val.txt',
    '/home/<USER>/JinWooChoi/class_mapping.txt',
    transform=val_transform
)

test_dataset = CervixDataset(
    '/home/<USER>/JinWooChoi/test.txt',
    '/home/<USER>/JinWooChoi/class_mapping.txt',
    transform=test_transform
)

# Create data loaders
train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=4)
val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=4)
test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False, num_workers=4)

# Get number of classes
num_classes = len(set(train_dataset.labels))
print(f"Number of classes: {num_classes}")
print(f"Class names: {train_dataset.classes}")

# 创建DINOv2分类器模型
# 可以选择不同的backbone: 'vit_small', 'vit_base', 'vit_large', 'vit_giant2'
# 如果有预训练权重，可以在pretrained_path参数中指定路径
model = DINOv2Classifier(
    backbone_name='vit_large',  # 使用Large模型
    num_classes=num_classes,
    freeze_backbone=True,  # 冻结backbone，只训练分类头
    pretrained_path=None,  # 如果有预训练权重，在这里指定路径
    dropout_rate=0.5
)

# 将模型移动到设备
model.to(device)

# 打印模型参数信息
total_params = sum(p.numel() for p in model.parameters())
trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
print(f"Total parameters: {total_params:,}")
print(f"Trainable parameters: {trainable_params:,}")

# 选择损失函数和优化器
criterion = nn.CrossEntropyLoss()

# 只优化可训练的参数
optimizer = optim.Adam(
    filter(lambda p: p.requires_grad, model.parameters()),
    lr=learning_rate,
    weight_decay=1e-4
)

# 使用余弦退火学习率调度器
scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=num_epochs)

# 记录损失和准确率
train_losses = []
val_losses = []
train_accuracies = []
val_accuracies = []

# CSV 文件写入
with open(csv_file_path, mode='w', newline='') as file:
    writer = csv.writer(file)
    writer.writerow(['Epoch', 'Train Loss', 'Train Accuracy', 'Val Loss', 'Val Accuracy'])  # 写入表头

print(f"\n{'='*60}")
print(f"Starting DINOv2 training...")
print(f"{'='*60}")

# 训练模型
for epoch in range(num_epochs):
    model.train()
    running_loss = 0.0
    correct = 0
    total = 0

    for batch_idx, (images, labels) in enumerate(train_loader):
        images, labels = images.to(device), labels.to(device)  # 移动到设备
        optimizer.zero_grad()
        outputs = model(images)
        loss = criterion(outputs, labels)
        loss.backward()
        optimizer.step()

        running_loss += loss.item()
        _, predicted = torch.max(outputs.data, 1)
        total += labels.size(0)
        correct += (predicted == labels).sum().item()

        # 打印批次进度
        if batch_idx % 10 == 0:
            print(f'Epoch [{epoch+1}/{num_epochs}], Batch [{batch_idx}/{len(train_loader)}], '
                  f'Loss: {loss.item():.4f}')

    train_loss = running_loss / len(train_loader)
    train_accuracy = 100 * correct / total
    train_losses.append(train_loss)
    train_accuracies.append(train_accuracy)

    # 验证模型
    model.eval()
    running_loss = 0.0
    correct = 0
    total = 0

    with torch.no_grad():
        for images, labels in val_loader:
            images, labels = images.to(device), labels.to(device)  # 移动到设备
            outputs = model(images)
            loss = criterion(outputs, labels)
            running_loss += loss.item()
            _, predicted = torch.max(outputs.data, 1)
            total += labels.size(0)
            correct += (predicted == labels).sum().item()

    val_loss = running_loss / len(val_loader)
    val_accuracy = 100 * correct / total
    val_losses.append(val_loss)
    val_accuracies.append(val_accuracy)

    print(f'Epoch [{epoch + 1}/{num_epochs}], '
          f'Train Loss: {train_loss:.4f}, Train Accuracy: {train_accuracy:.2f}%, '
          f'Val Loss: {val_loss:.4f}, Val Accuracy: {val_accuracy:.2f}%')

    # 保存训练过程数据到 CSV
    with open(csv_file_path, mode='a', newline='') as file:
        writer = csv.writer(file)
        writer.writerow([epoch + 1, train_loss, train_accuracy, val_loss, val_accuracy])

    # 更新学习率
    scheduler.step()

# 保存训练好的模型
torch.save(model.state_dict(), model_save_path)
print(f"\nModel saved to: {model_save_path}")

print(f"\n{'='*60}")
print(f"Starting DINOv2 evaluation...")
print(f"{'='*60}")

# 开始test部分
model.load_state_dict(torch.load(model_save_path))
model.eval()

# Get class names for evaluation
class_names = test_dataset.classes

# 初始化存储预测和真实标签的列表
all_preds = []
all_labels = []
all_probs = []  # 初始化存储概率的列表

# 测试模型
with torch.no_grad():
    for images, labels in test_loader:
        images, labels = images.to(device), labels.to(device)  # 移动到设备
        outputs = model(images)
        _, preds = torch.max(outputs, 1)

        # 获取概率输出
        probs = torch.nn.functional.softmax(outputs, dim=1)

        all_preds.extend(preds.cpu().numpy())  # 将预测结果移回 CPU
        all_labels.extend(labels.cpu().numpy())  # 将真实标签移回 CPU
        all_probs.append(probs.cpu().numpy())  # 将概率结果移回 CPU

# 将概率列表转换为numpy数组
all_probs = np.vstack(all_probs)

# 计算准确率、召回率和 F1 分数
all_preds = np.array(all_preds)
all_labels = np.array(all_labels)

# 生成分类报告
report = classification_report(all_labels, all_preds, target_names=class_names, output_dict=True, zero_division=0)
print(classification_report(all_labels, all_preds, target_names=class_names))

# 计算混淆矩阵
cm = confusion_matrix(all_labels, all_preds)
print("Confusion Matrix:")
print(cm)

# 计算每个类别的指标
print("\n===== 每个类别的评估指标 (DINOv2) =====")
n_classes = len(class_names)
class_accuracies = []
sensitivities = []
specificities = []
precisions = []  # 精确率列表
f1_scores = []  # F1分数列表
auc_scores = []

# 将标签转换为one-hot编码用于AUC计算

y_true_bin = label_binarize(all_labels, classes=range(n_classes))

for i in range(n_classes):
    # 获取当前类别的样本索引
    class_indices = np.where(all_labels == i)[0]

    if len(class_indices) > 0:
        # 计算当前类别的Top-1准确率
        class_accuracy = accuracy_score(all_labels[class_indices], all_preds[class_indices])
        class_accuracies.append(class_accuracy)
        print(f"Accuracy for {class_names[i]}: {class_accuracy:.4f}")

        # 计算敏感性(Sensitivity/Recall) - 即真阳性率(TPR)
        TP = cm[i, i]  # 真阳性
        FN = np.sum(cm[i, :]) - TP  # 假阴性
        sensitivity = TP / (TP + FN) if (TP + FN) > 0 else 0
        sensitivities.append(sensitivity)
        print(f"Sensitivity for {class_names[i]}: {sensitivity:.4f}")

        # 计算特异性(Specificity)
        FP = np.sum(cm[:, i]) - TP  # 假阳性
        TN = np.sum(cm) - TP - FP - FN  # 真阴性
        specificity = TN / (TN + FP) if (TN + FP) > 0 else 0
        specificities.append(specificity)
        print(f"Specificity for {class_names[i]}: {specificity:.4f}")

        # 计算精确率(Precision)
        precision = TP / (TP + FP) if (TP + FP) > 0 else 0
        precisions.append(precision)
        print(f"Precision for {class_names[i]}: {precision:.4f}")

        # 计算F1分数 - 精确率和召回率的调和平均值
        f1 = 2 * (precision * sensitivity) / (precision + sensitivity) if (precision + sensitivity) > 0 else 0
        f1_scores.append(f1)
        print(f"F1 Score for {class_names[i]}: {f1:.4f}")

        # 计算AUC
        if len(np.unique(y_true_bin[:, i])) > 1:  # 确保有正负样本
            auc = roc_auc_score(y_true_bin[:, i], all_probs[:, i])
            auc_scores.append(auc)
            print(f"AUC for {class_names[i]}: {auc:.4f}")
        else:
            print(f"AUC for {class_names[i]}: N/A (only one class present)")
    else:
        print(f"No samples for class {class_names[i]}")
    print("-" * 50)

# 计算全数据的宏观指标
print("\n===== 全数据宏观评估指标 (DINOv2) =====")

# 计算 Top-1 准确率
top1_accuracy = accuracy_score(all_labels, all_preds)
print(f'Overall Accuracy: {top1_accuracy:.4f}')

# 计算宏观精确率、召回率和F1分数
macro_precision = precision_score(all_labels, all_preds, average='macro')
macro_recall = recall_score(all_labels, all_preds, average='macro')
macro_f1 = f1_score(all_labels, all_preds, average='macro')

print(f"Macro-averaged Precision: {macro_precision:.4f}")
print(f"Macro-averaged Recall/Sensitivity: {macro_recall:.4f}")
print(f"Macro-averaged F1 Score: {macro_f1:.4f}")

# 计算微观精确率、召回率和F1分数
micro_precision = precision_score(all_labels, all_preds, average='micro')
micro_recall = recall_score(all_labels, all_preds, average='micro')
micro_f1 = f1_score(all_labels, all_preds, average='micro')

print(f"Micro-averaged Precision: {micro_precision:.4f}")
print(f"Micro-averaged Recall/Sensitivity: {micro_recall:.4f}")
print(f"Micro-averaged F1 Score: {micro_f1:.4f}")

# 计算宏观特异性(Macro-averaged Specificity)
macro_specificity = np.mean(specificities)
print(f"Macro-averaged Specificity: {macro_specificity:.4f}")

# 计算自定义宏观精确率和F1分数
custom_macro_precision = np.mean(precisions)
custom_macro_f1 = np.mean(f1_scores)
print(f"Custom Macro-averaged Precision: {custom_macro_precision:.4f}")
print(f"Custom Macro-averaged F1 Score: {custom_macro_f1:.4f}")

# 计算宏观AUC(Macro-averaged AUC)
if auc_scores:
    macro_auc = np.mean(auc_scores)
    print(f"Macro-averaged AUC: {macro_auc:.4f}")

# 计算微观AUC(Micro-averaged AUC)
try:
    # 对于多类别问题，微观AUC通常使用OvR(One-vs-Rest)策略
    micro_auc = roc_auc_score(y_true_bin, all_probs, average='micro')
    print(f"Micro-averaged AUC: {micro_auc:.4f}")
except:
    print("Could not calculate Micro-averaged AUC")

# 汇总所有宏观指标
print(f"\n{'='*60}")
print(f"DINOv2 宏观评估指标汇总")
print(f"{'='*60}")
print(f"Accuracy: {top1_accuracy:.4f}")
print(f"Macro-averaged Precision: {macro_precision:.4f}")
print(f"Macro-averaged Recall/Sensitivity: {macro_recall:.4f}")
print(f"Macro-averaged F1 Score: {macro_f1:.4f}")
print(f"Macro-averaged Specificity: {macro_specificity:.4f}")
if 'macro_auc' in locals():
    print(f"Macro-averaged AUC: {macro_auc:.4f}")
if 'micro_auc' in locals():
    print(f"Micro-averaged AUC: {micro_auc:.4f}")

# 保存评估结果到文件
results_file = model_save_path.replace('.pth', '_results.txt')
with open(results_file, 'w') as f:
    f.write("DINOv2 Model Evaluation Results\n")
    f.write("="*50 + "\n")
    f.write(f"Overall Accuracy: {top1_accuracy:.4f}\n")
    f.write(f"Macro-averaged Precision: {macro_precision:.4f}\n")
    f.write(f"Macro-averaged Recall: {macro_recall:.4f}\n")
    f.write(f"Macro-averaged F1 Score: {macro_f1:.4f}\n")
    f.write(f"Macro-averaged Specificity: {macro_specificity:.4f}\n")
    if 'macro_auc' in locals():
        f.write(f"Macro-averaged AUC: {macro_auc:.4f}\n")
    if 'micro_auc' in locals():
        f.write(f"Micro-averaged AUC: {micro_auc:.4f}\n")

print(f"\nResults saved to: {results_file}")
print(f"Training log saved to: {csv_file_path}")
print(f"\n{'='*60}")
print(f"DINOv2 training and evaluation completed!")
print(f"{'='*60}")

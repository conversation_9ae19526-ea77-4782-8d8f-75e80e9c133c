#!/usr/bin/env python3
"""
DINOv2导入问题修复脚本
自动检测和修复常见的导入问题
"""

import os
import sys
import shutil

def fix_init_file():
    """修复__init__.py文件"""
    print("Fixing __init__.py file...")
    
    current_dir = os.path.dirname(os.path.abspath(__file__))
    dinov2_path = os.path.join(current_dir, 'dinov2')
    init_file = os.path.join(dinov2_path, '__init__.py')
    
    if not os.path.exists(dinov2_path):
        print(f"✗ dinov2 directory not found: {dinov2_path}")
        return False
    
    # 创建或修复__init__.py
    init_content = '''"""
DINOv2 module initialization
"""

# Import vision transformer
try:
    from . import vision_transformer
    from .vision_transformer import *
except ImportError as e:
    print(f"Warning: Could not import vision_transformer: {e}")

# Build model function (if available)
def build_model(device=None, gpu_num=1, model_name='dinov2_vitl', ckpt_path=None):
    """Build DINOv2 model"""
    try:
        if 'vitl' in model_name or 'large' in model_name:
            model = vision_transformer.vit_large(patch_size=14)
        elif 'vitb' in model_name or 'base' in model_name:
            model = vision_transformer.vit_base(patch_size=14)
        elif 'vits' in model_name or 'small' in model_name:
            model = vision_transformer.vit_small(patch_size=14)
        else:
            model = vision_transformer.vit_large(patch_size=14)
        
        if ckpt_path and os.path.exists(ckpt_path):
            # Load checkpoint if provided
            checkpoint = torch.load(ckpt_path, map_location='cpu')
            model.load_state_dict(checkpoint, strict=False)
        
        if device:
            model = model.to(device)
        
        return model, model.embed_dim
    except Exception as e:
        print(f"Error building model: {e}")
        raise

def build_transform():
    """Build transform function"""
    import torchvision.transforms as transforms
    return transforms.Compose([
        transforms.Resize((224, 224)),
        transforms.ToTensor(),
        transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
    ])
'''
    
    try:
        with open(init_file, 'w') as f:
            f.write(init_content)
        print(f"✓ Created/updated {init_file}")
        return True
    except Exception as e:
        print(f"✗ Error writing {init_file}: {e}")
        return False

def create_simple_vision_transformer():
    """创建简化的vision_transformer.py（如果原文件有问题）"""
    print("Creating simplified vision_transformer.py...")
    
    current_dir = os.path.dirname(os.path.abspath(__file__))
    dinov2_path = os.path.join(current_dir, 'dinov2')
    vit_file = os.path.join(dinov2_path, 'vision_transformer.py')
    
    # 检查原文件是否存在且可读
    if os.path.exists(vit_file):
        try:
            with open(vit_file, 'r') as f:
                content = f.read()
            if len(content) > 1000:  # 如果文件看起来正常
                print("✓ Original vision_transformer.py seems fine")
                return True
        except Exception as e:
            print(f"Error reading original file: {e}")
    
    # 创建备份的简化版本
    backup_file = os.path.join(dinov2_path, 'vision_transformer_backup.py')
    
    simple_vit_content = '''"""
Simplified Vision Transformer for DINOv2
This is a minimal implementation to resolve import issues
"""

import torch
import torch.nn as nn
import math

class DinoVisionTransformer(nn.Module):
    """Simplified DINOv2 Vision Transformer"""
    
    def __init__(self, img_size=224, patch_size=14, embed_dim=1024, depth=24, num_heads=16):
        super().__init__()
        self.img_size = img_size
        self.patch_size = patch_size
        self.embed_dim = embed_dim
        self.depth = depth
        self.num_heads = num_heads
        
        # Patch embedding
        self.patch_embed = nn.Conv2d(3, embed_dim, kernel_size=patch_size, stride=patch_size)
        
        # Class token
        self.cls_token = nn.Parameter(torch.zeros(1, 1, embed_dim))
        
        # Position embedding
        num_patches = (img_size // patch_size) ** 2
        self.pos_embed = nn.Parameter(torch.zeros(1, num_patches + 1, embed_dim))
        
        # Transformer blocks (simplified)
        self.blocks = nn.ModuleList([
            nn.TransformerEncoderLayer(
                d_model=embed_dim,
                nhead=num_heads,
                dim_feedforward=embed_dim * 4,
                dropout=0.0,
                batch_first=True
            ) for _ in range(depth)
        ])
        
        # Layer norm
        self.norm = nn.LayerNorm(embed_dim)
        
        # Initialize weights
        self._init_weights()
    
    def _init_weights(self):
        """Initialize weights"""
        nn.init.trunc_normal_(self.cls_token, std=0.02)
        nn.init.trunc_normal_(self.pos_embed, std=0.02)
    
    def forward_features(self, x):
        """Forward pass for feature extraction"""
        B = x.shape[0]
        
        # Patch embedding
        x = self.patch_embed(x)  # B, embed_dim, H, W
        x = x.flatten(2).transpose(1, 2)  # B, N, embed_dim
        
        # Add class token
        cls_tokens = self.cls_token.expand(B, -1, -1)
        x = torch.cat((cls_tokens, x), dim=1)
        
        # Add position embedding
        x = x + self.pos_embed
        
        # Apply transformer blocks
        for block in self.blocks:
            x = block(x)
        
        # Apply final norm
        x = self.norm(x)
        
        return {
            'x_norm_clstoken': x[:, 0],  # Class token
            'x_norm_patchtokens': x[:, 1:],  # Patch tokens
            'x_prenorm': x,
            'masks': None
        }
    
    def forward(self, x):
        """Standard forward pass"""
        return self.forward_features(x)['x_norm_clstoken']

# Model creation functions
def vit_small(patch_size=14, **kwargs):
    """Create ViT-Small model"""
    return DinoVisionTransformer(
        patch_size=patch_size,
        embed_dim=384,
        depth=12,
        num_heads=6,
        **kwargs
    )

def vit_base(patch_size=14, **kwargs):
    """Create ViT-Base model"""
    return DinoVisionTransformer(
        patch_size=patch_size,
        embed_dim=768,
        depth=12,
        num_heads=12,
        **kwargs
    )

def vit_large(patch_size=14, **kwargs):
    """Create ViT-Large model"""
    return DinoVisionTransformer(
        patch_size=patch_size,
        embed_dim=1024,
        depth=24,
        num_heads=16,
        **kwargs
    )

def vit_giant2(patch_size=14, **kwargs):
    """Create ViT-Giant2 model"""
    return DinoVisionTransformer(
        patch_size=patch_size,
        embed_dim=1536,
        depth=40,
        num_heads=24,
        **kwargs
    )
'''
    
    try:
        with open(backup_file, 'w') as f:
            f.write(simple_vit_content)
        print(f"✓ Created backup vision transformer: {backup_file}")
        
        # 如果原文件不存在或有问题，使用备份版本
        if not os.path.exists(vit_file):
            shutil.copy2(backup_file, vit_file)
            print(f"✓ Copied backup to main file: {vit_file}")
        
        return True
    except Exception as e:
        print(f"✗ Error creating backup vision transformer: {e}")
        return False

def main():
    """主修复函数"""
    print("=" * 60)
    print("DINOv2 Import Fix Script")
    print("=" * 60)
    
    current_dir = os.path.dirname(os.path.abspath(__file__))
    dinov2_path = os.path.join(current_dir, 'dinov2')
    
    print(f"Working directory: {current_dir}")
    print(f"DINOv2 path: {dinov2_path}")
    
    if not os.path.exists(dinov2_path):
        print(f"✗ DINOv2 directory not found: {dinov2_path}")
        print("Please ensure the dinov2 directory exists in the same folder as this script.")
        return
    
    print("✓ DINOv2 directory found")
    
    # 修复步骤
    steps = [
        ("Fixing __init__.py", fix_init_file),
        ("Creating backup vision transformer", create_simple_vision_transformer),
    ]
    
    success_count = 0
    for step_name, step_func in steps:
        print(f"\n{step_name}...")
        try:
            if step_func():
                success_count += 1
                print(f"✓ {step_name} completed")
            else:
                print(f"✗ {step_name} failed")
        except Exception as e:
            print(f"✗ {step_name} failed with error: {e}")
    
    print(f"\n{'='*60}")
    print(f"Fix Results: {success_count}/{len(steps)} steps completed")
    
    if success_count == len(steps):
        print("🎉 All fixes applied successfully!")
        print("\nYou can now try running:")
        print("  python diagnose_dinov2.py  # To verify the fixes")
        print("  python dinov2_simple.py    # To run training")
    else:
        print("❌ Some fixes failed. Please check the errors above.")
    
    print("=" * 60)

if __name__ == "__main__":
    main()

# DINOv2训练和评估使用说明

本项目基于原始的`vit_all.py`结构，将Vision Transformer替换为DINOv2模型，用于图像分类任务的训练和评估。

## 📁 文件结构

```
dinov2/                          # DINOv2模型实现
├── __init__.py                  # 模块初始化
├── vision_transformer.py       # Vision Transformer实现
└── layers/                      # 模型层实现
    ├── __init__.py
    ├── attention.py            # 注意力机制
    ├── block.py               # Transformer块
    ├── dino_head.py           # DINO头部
    ├── mlp.py                 # MLP层
    ├── patch_embed.py         # 图像块嵌入
    └── ...

dinov2_all.py                   # 完整版训练脚本（仿照vit_all.py）
dinov2_train.py                 # 简化版训练脚本（使用配置文件）
dinov2_config.py                # 配置文件
DINOv2_使用说明.md              # 本说明文档
requirements.txt                # 依赖项
```

## 🚀 快速开始

### 1. 环境准备

```bash
# 安装依赖
pip install -r requirements.txt

# 或手动安装
pip install torch torchvision numpy Pillow scikit-learn
pip install xformers  # 可选，用于性能优化
```

### 2. 数据准备

确保您的数据文件按以下格式组织：

```
/home/<USER>/JinWooChoi/
├── train.txt              # 训练集文件列表
├── val.txt                # 验证集文件列表  
├── test.txt               # 测试集文件列表
└── class_mapping.txt      # 类别映射文件
```

**文件格式示例：**

`train.txt`:
```
/path/to/image1.jpg class_name_1
/path/to/image2.jpg class_name_2
...
```

`class_mapping.txt`:
```
class_name_1 0
class_name_2 1
...
```

### 3. 配置参数

编辑 `dinov2_config.py` 文件，修改以下关键参数：

```python
# 数据路径
TRAIN_TXT_PATH = '/home/<USER>/JinWooChoi/train.txt'
VAL_TXT_PATH = '/home/<USER>/JinWooChoi/val.txt'
TEST_TXT_PATH = '/home/<USER>/JinWooChoi/test.txt'
CLASS_MAPPING_PATH = '/home/<USER>/JinWooChoi/class_mapping.txt'

# 模型配置
BACKBONE_NAME = 'vit_large'  # 选择模型大小
FREEZE_BACKBONE = True       # 是否冻结backbone

# 训练参数
LEARNING_RATE = 0.001
BATCH_SIZE = 32
NUM_EPOCHS = 50
```

### 4. 运行训练

有两种方式运行训练：

**方式1：使用完整版脚本**
```bash
python dinov2_all.py
```

**方式2：使用配置文件版本**
```bash
python dinov2_train.py
```

## 🔧 模型配置选项

### DINOv2模型规格

| 模型名称 | 嵌入维度 | 层数 | 注意力头 | 参数量 | 推荐用途 |
|----------|----------|------|----------|--------|----------|
| vit_small | 384 | 12 | 6 | ~22M | 快速实验 |
| vit_base | 768 | 12 | 12 | ~86M | 平衡性能 |
| vit_large | 1024 | 24 | 16 | ~307M | 高性能 |
| vit_giant2 | 1536 | 40 | 24 | ~1.1B | 最佳性能 |

### 训练策略

**冻结backbone (推荐)**
- `FREEZE_BACKBONE = True`
- 只训练分类头，训练速度快
- 适合数据量较小的情况

**端到端训练**
- `FREEZE_BACKBONE = False`  
- 训练整个模型，可能获得更好性能
- 需要更多计算资源和训练时间

## 📊 输出文件

训练完成后会生成以下文件：

```
/home/<USER>/JinWooChoi/DINOv2/
├── train_performance.csv           # 训练过程记录
├── trained_dinov2_model.pth        # 最终模型权重
├── best_dinov2_model.pth          # 最佳模型权重
└── trained_dinov2_model_results.txt # 评估结果
```

## 📈 评估指标

脚本会自动计算以下评估指标：

### 整体指标
- **Accuracy**: 整体准确率
- **Macro-averaged Precision**: 宏平均精确率
- **Macro-averaged Recall**: 宏平均召回率  
- **Macro-averaged F1 Score**: 宏平均F1分数
- **Macro-averaged Specificity**: 宏平均特异性
- **Macro-averaged AUC**: 宏平均AUC

### 每类别指标
- 每个类别的准确率、敏感性、特异性、精确率、F1分数、AUC

## 🔍 使用预训练权重

如果您有DINOv2的预训练权重：

1. 将权重文件放在合适位置
2. 在配置文件中设置路径：
```python
PRETRAINED_PATH = '/path/to/dinov2_pretrained_weights.pth'
```

## ⚡ 性能优化建议

### 内存优化
- 使用较小的batch size（如16或8）
- 选择较小的模型（vit_small或vit_base）
- 冻结backbone参数

### 速度优化
- 安装xFormers：`pip install xformers`
- 使用多个数据加载worker：`NUM_WORKERS = 4`
- 使用GPU训练

### 精度优化
- 使用更大的模型（vit_large或vit_giant2）
- 增加训练轮数
- 调整学习率和数据增强参数

## 🐛 常见问题

**Q: 内存不足怎么办？**
A: 减小batch size，选择较小的模型，或使用梯度累积。

**Q: 训练速度太慢？**
A: 安装xFormers，冻结backbone，使用较小模型。

**Q: 如何使用自己的数据？**
A: 按照指定格式准备txt文件和class_mapping文件，修改配置文件中的路径。

**Q: 如何调整超参数？**
A: 编辑`dinov2_config.py`文件中的相应参数。

## 📞 技术支持

如果遇到问题，请检查：
1. 数据文件格式是否正确
2. 路径设置是否正确
3. 依赖项是否完整安装
4. GPU内存是否足够

## 🔄 与原版vit_all.py的对比

| 特性 | vit_all.py | dinov2_all.py |
|------|------------|---------------|
| 模型 | timm ViT | DINOv2 |
| 预训练 | ImageNet | 自监督学习 |
| 特征质量 | 标准 | 更高质量 |
| 配置灵活性 | 固定 | 可配置 |
| 评估指标 | 完整 | 完整 |

DINOv2版本保持了原版的所有功能，同时提供了更强大的特征提取能力和更灵活的配置选项。

#!/usr/bin/env python3
"""
DINOv2简化版训练脚本
解决模块导入问题的版本
"""

import torch.optim as optim
import csv
import os
from PIL import Image
import torch
import torch.nn as nn
import torchvision.transforms as transforms
from torch.utils.data import DataLoader
from sklearn.metrics import classification_report, accuracy_score, roc_auc_score, confusion_matrix
from sklearn.metrics import precision_score, recall_score, f1_score
from sklearn.preprocessing import label_binarize
import numpy as np
import sys

# 判断是否可用GPU
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"Using device: {device}")

# 定义超参数
learning_rate = 0.001
batch_size = 32
num_epochs = 50
csv_file_path = r'/home/<USER>/Cytofm/JinWooChoi/train_performance.csv'
model_save_path = r'/home/<USER>/Cytofm/JinWooChoi/trained_dinov2_model.pth'

# 创建保存目录
os.makedirs(os.path.dirname(csv_file_path), exist_ok=True)
os.makedirs(os.path.dirname(model_save_path), exist_ok=True)

# 尝试导入DINOv2模块
def import_dinov2():
    """尝试导入DINOv2模块"""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 方法1: 尝试直接导入
    try:
        sys.path.insert(0, current_dir)
        import dinov2
        from dinov2 import vision_transformer as vits
        print("✓ Method 1: DINOv2 imported successfully")
        return dinov2, vits
    except ImportError:
        print("Method 1 failed, trying method 2...")
    
    # 方法2: 尝试从dinov2目录导入
    try:
        dinov2_path = os.path.join(current_dir, 'dinov2')
        sys.path.insert(0, dinov2_path)
        import vision_transformer as vits
        
        # 创建dinov2命名空间
        class DinOv2Module:
            vision_transformer = vits
        
        dinov2 = DinOv2Module()
        print("✓ Method 2: DINOv2 imported successfully")
        return dinov2, vits
    except ImportError:
        print("Method 2 failed, trying method 3...")
    
    # 方法3: 手动导入所需函数
    try:
        dinov2_path = os.path.join(current_dir, 'dinov2')
        vit_path = os.path.join(dinov2_path, 'vision_transformer.py')
        
        if os.path.exists(vit_path):
            # 使用exec导入
            spec = {}
            with open(vit_path, 'r') as f:
                exec(f.read(), spec)
            
            # 创建vits模块
            class VitsModule:
                def __init__(self, spec):
                    for name, obj in spec.items():
                        if not name.startswith('__'):
                            setattr(self, name, obj)
            
            vits = VitsModule(spec)
            
            # 创建dinov2模块
            class DinOv2Module:
                vision_transformer = vits
            
            dinov2 = DinOv2Module()
            print("✓ Method 3: DINOv2 imported successfully")
            return dinov2, vits
        else:
            raise ImportError(f"vision_transformer.py not found at {vit_path}")
    except Exception as e:
        print(f"Method 3 failed: {e}")
    
    # 如果所有方法都失败
    print("❌ All import methods failed!")
    print(f"Current directory: {current_dir}")
    print("Please ensure the dinov2 directory is present with the following structure:")
    print("  dinov2/")
    print("  ├── __init__.py")
    print("  ├── vision_transformer.py")
    print("  └── layers/")
    
    # 检查目录结构
    dinov2_path = os.path.join(current_dir, 'dinov2')
    if os.path.exists(dinov2_path):
        print(f"\nFound dinov2 directory at: {dinov2_path}")
        try:
            files = os.listdir(dinov2_path)
            print(f"Contents: {files}")
        except:
            pass
    else:
        print(f"\n❌ dinov2 directory not found at: {dinov2_path}")
    
    sys.exit(1)

# 导入DINOv2
dinov2, vits = import_dinov2()

# Custom dataset class
class CervixDataset(torch.utils.data.Dataset):
    def __init__(self, txt_file, class_mapping_file, transform=None):
        self.data = []
        self.labels = []
        self.transform = transform
        self.classes = []

        # Load class mapping
        class_mapping = {}
        with open(class_mapping_file, 'r') as f:
            for line in f:
                parts = line.strip().split()
                if len(parts) >= 2:
                    class_name = ' '.join(parts[:-1])
                    idx = int(parts[-1])
                    class_mapping[class_name] = idx
                    while len(self.classes) <= idx:
                        self.classes.append("")
                    self.classes[idx] = class_name

        # Read TXT file
        with open(txt_file, 'r') as f:
            for line in f:
                parts = line.strip().split()
                if len(parts) > 1:
                    img_path = parts[0]
                    class_label = ' '.join(parts[1:])
                    class_idx = class_mapping[class_label]
                    self.data.append(img_path)
                    self.labels.append(class_idx)

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):
        img_path = self.data[idx]
        label = self.labels[idx]

        if not os.path.exists(img_path):
            raise FileNotFoundError(f"Image not found: {img_path}")

        image = Image.open(img_path).convert('RGB')
        if self.transform:
            image = self.transform(image)
        return image, label

# DINOv2分类器类
class DINOv2Classifier(nn.Module):
    def __init__(self, backbone_name='vit_large', num_classes=10, freeze_backbone=True, dropout_rate=0.5):
        super().__init__()
        
        # 创建DINOv2 backbone
        try:
            if backbone_name == 'vit_small':
                self.backbone = vits.vit_small(patch_size=14)
            elif backbone_name == 'vit_base':
                self.backbone = vits.vit_base(patch_size=14)
            elif backbone_name == 'vit_large':
                self.backbone = vits.vit_large(patch_size=14)
            elif backbone_name == 'vit_giant2':
                self.backbone = vits.vit_giant2(patch_size=14)
            else:
                raise ValueError(f"Unsupported backbone: {backbone_name}")
        except Exception as e:
            print(f"Error creating backbone {backbone_name}: {e}")
            print("Trying to use vit_base as fallback...")
            try:
                self.backbone = vits.vit_base(patch_size=14)
                backbone_name = 'vit_base'
            except Exception as e2:
                print(f"Fallback also failed: {e2}")
                raise
        
        # 冻结backbone参数
        if freeze_backbone:
            for param in self.backbone.parameters():
                param.requires_grad = False
            print("DINOv2 backbone frozen")
        
        # 获取嵌入维度
        embed_dim = self.backbone.embed_dim
        
        # 分类头
        self.classifier = nn.Sequential(
            nn.Dropout(p=dropout_rate),
            nn.Linear(embed_dim, 512),
            nn.ReLU(),
            nn.Dropout(p=dropout_rate/2),
            nn.Linear(512, num_classes)
        )
        
        print(f"DINOv2 Classifier created:")
        print(f"  - Backbone: {backbone_name}")
        print(f"  - Embed dim: {embed_dim}")
        print(f"  - Num classes: {num_classes}")
    
    def forward(self, x):
        # 提取特征
        if not any(p.requires_grad for p in self.backbone.parameters()):
            with torch.no_grad():
                features = self.backbone.forward_features(x)
                cls_features = features['x_norm_clstoken']
        else:
            features = self.backbone.forward_features(x)
            cls_features = features['x_norm_clstoken']
        
        return self.classifier(cls_features)

def main():
    """主函数"""
    print("Starting DINOv2 training...")
    
    # 数据预处理
    train_transform = transforms.Compose([
        transforms.Resize((224, 224)),
        transforms.RandomHorizontalFlip(),
        transforms.RandomRotation(10),
        transforms.ColorJitter(brightness=0.2, contrast=0.2, saturation=0.2, hue=0.1),
        transforms.ToTensor(),
        transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
    ])

    val_transform = transforms.Compose([
        transforms.Resize((224, 224)),
        transforms.ToTensor(),
        transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
    ])

    # 创建数据集
    train_dataset = CervixDataset(
        '/home/<USER>/JinWooChoi/train.txt',
        '/home/<USER>/JinWooChoi/class_mapping.txt',
        transform=train_transform
    )

    val_dataset = CervixDataset(
        '/home/<USER>/JinWooChoi/val.txt',
        '/home/<USER>/JinWooChoi/class_mapping.txt',
        transform=val_transform
    )

    test_dataset = CervixDataset(
        '/home/<USER>/JinWooChoi/test.txt',
        '/home/<USER>/JinWooChoi/class_mapping.txt',
        transform=val_transform
    )

    # 数据加载器
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=4)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=4)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False, num_workers=4)

    num_classes = len(set(train_dataset.labels))
    print(f"Number of classes: {num_classes}")

    # 创建模型
    model = DINOv2Classifier(
        backbone_name='vit_large',
        num_classes=num_classes,
        freeze_backbone=True,
        dropout_rate=0.5
    ).to(device)

    # 优化器和损失函数
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.Adam(
        filter(lambda p: p.requires_grad, model.parameters()), 
        lr=learning_rate, 
        weight_decay=1e-4
    )
    scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=num_epochs)

    # 训练记录
    train_losses, val_losses = [], []
    train_accuracies, val_accuracies = [], []

    # CSV记录
    with open(csv_file_path, mode='w', newline='') as file:
        writer = csv.writer(file)
        writer.writerow(['Epoch', 'Train Loss', 'Train Accuracy', 'Val Loss', 'Val Accuracy'])

    print(f"Starting training for {num_epochs} epochs...")

if __name__ == "__main__":
    main()

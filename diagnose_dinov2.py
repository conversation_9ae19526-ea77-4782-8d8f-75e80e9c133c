#!/usr/bin/env python3
"""
DINOv2环境诊断脚本
帮助诊断和解决导入问题
"""

import os
import sys
import importlib.util

def check_python_environment():
    """检查Python环境"""
    print("=" * 60)
    print("Python Environment Check")
    print("=" * 60)
    print(f"Python version: {sys.version}")
    print(f"Python executable: {sys.executable}")
    print(f"Current working directory: {os.getcwd()}")
    print(f"Script location: {os.path.abspath(__file__)}")
    print()

def check_required_packages():
    """检查必需的包"""
    print("=" * 60)
    print("Required Packages Check")
    print("=" * 60)
    
    required_packages = [
        'torch',
        'torchvision', 
        'numpy',
        'PIL',
        'sklearn',
        'csv'
    ]
    
    for package in required_packages:
        try:
            if package == 'PIL':
                import PIL
                print(f"✓ {package}: {PIL.__version__}")
            elif package == 'csv':
                import csv
                print(f"✓ {package}: built-in module")
            else:
                module = __import__(package)
                version = getattr(module, '__version__', 'unknown')
                print(f"✓ {package}: {version}")
        except ImportError:
            print(f"✗ {package}: NOT FOUND")
    print()

def check_directory_structure():
    """检查目录结构"""
    print("=" * 60)
    print("Directory Structure Check")
    print("=" * 60)

    current_dir = os.path.dirname(os.path.abspath(__file__))
    print(f"Current directory: {current_dir}")

    # 检查多个可能的dinov2位置
    possible_dinov2_paths = [
        os.path.join(current_dir, 'dinov2'),  # 同目录下
        os.path.join(os.path.dirname(current_dir), 'dinov2'),  # 上级目录
        '/home/<USER>/dinov2',  # 绝对路径
        os.path.join(current_dir, '..', 'dinov2'),  # 相对路径到上级
    ]

    dinov2_found = False
    working_dinov2_path = None

    for i, dinov2_path in enumerate(possible_dinov2_paths, 1):
        abs_path = os.path.abspath(dinov2_path)
        print(f"\nLocation {i}: {abs_path}")

        if os.path.exists(abs_path):
            print(f"  ✓ Directory exists")

            # 检查关键文件
            key_files = [
                '__init__.py',
                'vision_transformer.py'
            ]

            files_found = 0
            for file in key_files:
                file_path = os.path.join(abs_path, file)
                if os.path.exists(file_path):
                    size = os.path.getsize(file_path)
                    print(f"    ✓ {file} ({size} bytes)")
                    files_found += 1
                else:
                    print(f"    ✗ {file} NOT FOUND")

            # 检查layers目录
            layers_path = os.path.join(abs_path, 'layers')
            if os.path.exists(layers_path):
                print(f"    ✓ layers directory found")
                try:
                    layer_files = os.listdir(layers_path)
                    print(f"      Layer files: {layer_files[:5]}...")
                except:
                    pass
            else:
                print(f"    ✗ layers directory NOT FOUND")

            # 如果找到了关键文件，标记为工作目录
            if files_found >= 1:  # 至少需要vision_transformer.py
                dinov2_found = True
                working_dinov2_path = abs_path
                print(f"    🎯 This location can be used!")

            # 列出dinov2目录所有内容
            try:
                all_files = os.listdir(abs_path)
                print(f"    All files: {all_files[:10]}...")  # 显示前10个
            except Exception as e:
                print(f"    Error listing contents: {e}")
        else:
            print(f"  ✗ Directory not found")

    if dinov2_found:
        print(f"\n🎉 Working dinov2 directory found at: {working_dinov2_path}")
    else:
        print(f"\n❌ No working dinov2 directory found!")

    print()

def test_dinov2_import():
    """测试DINOv2导入"""
    print("=" * 60)
    print("DINOv2 Import Test")
    print("=" * 60)
    
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 方法1: 直接导入
    print("Method 1: Direct import")
    try:
        sys.path.insert(0, current_dir)
        import dinov2
        print("✓ dinov2 imported successfully")
        
        try:
            from dinov2 import vision_transformer as vits
            print("✓ vision_transformer imported successfully")
            
            # 测试创建模型
            try:
                model = vits.vit_base(patch_size=14)
                print(f"✓ vit_base model created (embed_dim: {model.embed_dim})")
            except Exception as e:
                print(f"✗ Model creation failed: {e}")
                
        except ImportError as e:
            print(f"✗ vision_transformer import failed: {e}")
    except ImportError as e:
        print(f"✗ dinov2 import failed: {e}")
    
    print()
    
    # 方法2: 从dinov2目录导入
    print("Method 2: Import from dinov2 directory")
    try:
        dinov2_path = os.path.join(current_dir, 'dinov2')
        sys.path.insert(0, dinov2_path)
        
        import vision_transformer as vits
        print("✓ vision_transformer imported from dinov2 directory")
        
        # 测试创建模型
        try:
            model = vits.vit_base(patch_size=14)
            print(f"✓ vit_base model created (embed_dim: {model.embed_dim})")
        except Exception as e:
            print(f"✗ Model creation failed: {e}")
            
    except ImportError as e:
        print(f"✗ Import from dinov2 directory failed: {e}")
    
    print()

def check_data_files():
    """检查数据文件"""
    print("=" * 60)
    print("Data Files Check")
    print("=" * 60)
    
    data_files = [
        '/home/<USER>/JinWooChoi/train.txt',
        '/home/<USER>/JinWooChoi/val.txt', 
        '/home/<USER>/JinWooChoi/test.txt',
        '/home/<USER>/JinWooChoi/class_mapping.txt'
    ]
    
    for file_path in data_files:
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            print(f"✓ {os.path.basename(file_path)}: {file_path} ({size} bytes)")
            
            # 显示前几行内容
            try:
                with open(file_path, 'r') as f:
                    lines = f.readlines()[:3]
                    print(f"  First 3 lines:")
                    for i, line in enumerate(lines, 1):
                        print(f"    {i}: {line.strip()}")
            except Exception as e:
                print(f"  Error reading file: {e}")
        else:
            print(f"✗ {os.path.basename(file_path)}: {file_path} NOT FOUND")
        print()

def provide_solutions():
    """提供解决方案"""
    print("=" * 60)
    print("Possible Solutions")
    print("=" * 60)
    
    print("If dinov2 import fails:")
    print("1. Ensure the dinov2 directory is in the same folder as your script")
    print("2. Check that dinov2/__init__.py exists and is not empty")
    print("3. Try running: export PYTHONPATH=$PYTHONPATH:$(pwd)")
    print("4. Use the dinov2_simple.py script which has better error handling")
    print()
    
    print("If data files are missing:")
    print("1. Update the file paths in the script to match your actual data location")
    print("2. Ensure the data files follow the correct format")
    print("3. Check file permissions")
    print()
    
    print("If package imports fail:")
    print("1. Install missing packages: pip install torch torchvision scikit-learn pillow")
    print("2. Check if you're using the correct Python environment")
    print("3. Try: pip install --upgrade <package_name>")
    print()

def main():
    """主函数"""
    print("DINOv2 Environment Diagnosis")
    print("This script will help diagnose import and setup issues")
    print()
    
    check_python_environment()
    check_required_packages()
    check_directory_structure()
    test_dinov2_import()
    check_data_files()
    provide_solutions()
    
    print("=" * 60)
    print("Diagnosis Complete")
    print("=" * 60)

if __name__ == "__main__":
    main()

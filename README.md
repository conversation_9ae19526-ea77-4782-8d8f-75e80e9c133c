# DINOv2 Vision Transformer 使用指南

这是一个基于Meta开发的DINOv2（DINO version 2）自监督学习Vision Transformer模型的实现。

## 📋 模型简介

**DINOv2** 是一个强大的自监督学习视觉模型，具有以下特点：

- 🔥 **自监督学习**：无需标注数据即可学习高质量的视觉表示
- 🏗️ **Vision Transformer架构**：基于Transformer的图像处理模型
- 🎯 **多尺度支持**：支持Small、Base、Large、Giant等不同规模的模型
- ⚡ **高效推理**：集成xFormers优化，支持高效的注意力计算
- 🔧 **灵活应用**：可用于特征提取、图像分类、目标检测等多种下游任务

## 🚀 快速开始

### 1. 环境安装

```bash
# 安装基础依赖
pip install -r requirements.txt

# 或者手动安装
pip install torch torchvision numpy Pillow
pip install xformers  # 可选，用于性能优化
```

### 2. 最简单的使用示例

```python
import torch
import dinov2
from dinov2 import vision_transformer as vits

# 创建模型
model = vits.vit_large(patch_size=14)
model.eval()

# 输入图像 (batch_size=1, channels=3, height=224, width=224)
x = torch.randn(1, 3, 224, 224)

# 获取特征
with torch.no_grad():
    features = model(x)  # 全局特征 [1, 1024]
    print(f"特征形状: {features.shape}")
```

运行快速示例：
```bash
python quick_start.py
```

## 📖 详细使用方法

### 1. 模型创建

```python
from dinov2 import vision_transformer as vits

# 创建不同规模的模型
model_small = vits.vit_small(patch_size=14)    # 384维嵌入
model_base = vits.vit_base(patch_size=14)      # 768维嵌入  
model_large = vits.vit_large(patch_size=14)    # 1024维嵌入
model_giant = vits.vit_giant2(patch_size=14)   # 1536维嵌入
```

### 2. 加载预训练权重

```python
import dinov2

# 如果有预训练权重文件
model, embed_dim = dinov2.build_model(
    device='cuda',
    gpu_num=1,
    model_name='dinov2_vitl',  # 或 'gpfm', 'ccs'
    ckpt_path='path/to/pretrained_weights.pth'
)
```

### 3. 数据预处理

```python
# 使用内置的预处理
transform = dinov2.build_transform()

# 或者手动创建
from torchvision import transforms
transform = transforms.Compose([
    transforms.Resize((224, 224)),
    transforms.ToTensor(),
    transforms.Normalize(mean=[0.485, 0.456, 0.406], 
                        std=[0.229, 0.224, 0.225])
])
```

### 4. 特征提取

```python
# 详细特征提取
features = model.forward_features(x)
cls_features = features['x_norm_clstoken']      # 全局特征 [B, embed_dim]
patch_features = features['x_norm_patchtokens'] # 局部特征 [B, num_patches, embed_dim]

# 获取中间层特征
intermediate = model.get_intermediate_layers(x, n=4, return_class_token=True)
```

## 🏋️ 训练示例

### 微调训练

运行完整的训练示例：
```bash
python training_example.py
```

### 自定义分类器

```python
import torch.nn as nn

class CustomClassifier(nn.Module):
    def __init__(self, backbone, num_classes):
        super().__init__()
        self.backbone = backbone
        # 冻结backbone参数
        for param in self.backbone.parameters():
            param.requires_grad = False
            
        # 自定义分类头
        self.classifier = nn.Sequential(
            nn.Linear(backbone.embed_dim, 512),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(512, num_classes)
        )
    
    def forward(self, x):
        with torch.no_grad():
            features = self.backbone.forward_features(x)
            cls_features = features['x_norm_clstoken']
        return self.classifier(cls_features)
```

## 📁 文件结构

```
dinov2/
├── __init__.py              # 主模块入口
├── vision_transformer.py   # Vision Transformer实现
├── layers/                  # 模型层实现
│   ├── __init__.py
│   ├── attention.py        # 注意力机制
│   ├── block.py           # Transformer块
│   ├── dino_head.py       # DINO头部
│   ├── mlp.py             # MLP层
│   ├── patch_embed.py     # 图像块嵌入
│   └── ...
├── dinov2_usage_example.py # 完整使用示例
├── quick_start.py          # 快速入门
├── training_example.py     # 训练示例
└── requirements.txt        # 依赖项
```

## 🔧 模型配置

### 支持的模型规格

| 模型 | 嵌入维度 | 层数 | 注意力头 | 参数量 |
|------|----------|------|----------|--------|
| ViT-S | 384 | 12 | 6 | ~22M |
| ViT-B | 768 | 12 | 12 | ~86M |
| ViT-L | 1024 | 24 | 16 | ~307M |
| ViT-g | 1536 | 40 | 24 | ~1.1B |

### 关键参数

- `patch_size`: 图像块大小 (14 或 16)
- `img_size`: 输入图像尺寸 (默认224)
- `ffn_layer`: FFN层类型 ('mlp', 'swiglufused')
- `block_chunks`: 块分组数量 (用于内存优化)

## ⚠️ 注意事项

1. **内存需求**：Large和Giant模型需要较大的GPU内存
2. **xFormers**：安装xFormers可以显著提升性能
3. **预训练权重**：需要从官方或其他来源获取预训练权重
4. **输入尺寸**：模型期望224x224的输入图像

## 🤝 常见问题

**Q: 如何获取预训练权重？**
A: 需要从Meta官方或其他研究机构获取DINOv2的预训练权重文件。

**Q: 可以处理不同尺寸的图像吗？**
A: 模型内部会调整位置编码以适应不同尺寸，但建议使用224x224以获得最佳性能。

**Q: 如何用于目标检测？**
A: 可以使用patch特征进行密集预测任务，或者作为检测器的backbone。

## 📚 参考资料

- [DINOv2 论文](https://arxiv.org/abs/2304.07193)
- [Meta官方实现](https://github.com/facebookresearch/dinov2)
- [Vision Transformer论文](https://arxiv.org/abs/2010.11929)
